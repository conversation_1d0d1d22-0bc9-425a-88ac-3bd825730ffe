### API Test cho Sản phẩm <PERSON><PERSON><PERSON> (Physical Product)
### Base URL: http://localhost:3000/api/v1

### Variables
@baseUrl = http://localhost:3000/api/v1
@token = YOUR_JWT_TOKEN_HERE

### 1. Tạ<PERSON> sản phẩm vật lý - DTO đầy đủ với tất cả trường
POST {{baseUrl}}/user/products
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "productType": "PHYSICAL",
  "name": "Áo thun nam cao cấp",
  "description": "<PERSON><PERSON> thun nam chất liệu cotton 100% cao cấp, tho<PERSON><PERSON> mát, thấm hút mồ hôi tốt. Thiết kế basic phù hợp mọi lứa tuổi.",
  "price": {
    "listPrice": 300000,
    "salePrice": 250000,
    "currency": "VND"
  },
  "imagesMediaTypes": [
    "image/jpeg",
    "image/png"
  ],
  "tags": [
    "áo thun",
    "nam",
    "cotton",
    "thời trang",
    "basic"
  ],
  "customFields": [
    {
      "customFieldId": 1,
      "value": {
        "value": "XL"
      }
    },
    {
      "customFieldId": 2,
      "value": {
        "value": "Đen"
      }
    }
  ],
  "shipmentConfig": {
    "widthCm": 25,
    "heightCm": 5,
    "lengthCm": 30,
    "weightGram": 200
  },
  "inventory": [
    {
      "warehouseId": 1,
      "availableQuantity": 100,
      "sku": "SHIRT-001-XL-BLACK",
      "barcode": "1234567890123"
    },
    {
      "warehouseId": 2,
      "availableQuantity": 50,
      "sku": "SHIRT-001-XL-BLACK-WH2",
      "barcode": "1234567890124"
    }
  ],
  "classifications": [
    {
      "type": "Màu sắc",
      "price": {
        "listPrice": 300000,
        "salePrice": 250000,
        "currency": "VND"
      },
      "description": "Phân loại theo màu sắc",
      "customFields": [
        {
          "customFieldId": 3,
          "value": {
            "value": "Đen"
          }
        }
      ],
      "imagesMediaTypes": ["image/jpeg"]
    },
    {
      "type": "Kích thước",
      "price": {
        "listPrice": 320000,
        "salePrice": 270000,
        "currency": "VND"
      },
      "description": "Phân loại theo kích thước",
      "customFields": [
        {
          "customFieldId": 4,
          "value": {
            "value": "XL"
          }
        }
      ],
      "imagesMediaTypes": ["image/jpeg"]
    }
  ]
}

### 2. Tạo sản phẩm vật lý - Sử dụng inventory có sẵn
POST {{baseUrl}}/user/products
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "productType": "PHYSICAL",
  "name": "Quần jean nam",
  "description": "Quần jean nam chất liệu denim cao cấp",
  "price": {
    "listPrice": 500000,
    "salePrice": 450000,
    "currency": "VND"
  },
  "inventory": [
    {
      "inventoryId": 123
    }
  ]
}

### 3. Tạo sản phẩm vật lý - Minimum required fields
POST {{baseUrl}}/user/products
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "productType": "PHYSICAL",
  "name": "Sản phẩm test tối thiểu",
  "price": {
    "listPrice": 100000,
    "salePrice": 90000,
    "currency": "VND"
  },
  "inventory": [
    {
      "warehouseId": 1,
      "availableQuantity": 10
    }
  ]
}

### 4. Tạo sản phẩm vật lý - Với giá dạng string
POST {{baseUrl}}/user/products
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "productType": "PHYSICAL",
  "name": "Sản phẩm giá liên hệ",
  "description": "Sản phẩm có giá theo thỏa thuận",
  "price": {
    "priceDescription": "Liên hệ để biết giá"
  },
  "inventory": [
    {
      "warehouseId": 1,
      "availableQuantity": 5,
      "sku": "CONTACT-001"
    }
  ]
}

### 5. Lấy danh sách sản phẩm
GET {{baseUrl}}/user/products?page=1&limit=10&productType=PHYSICAL
Authorization: Bearer {{token}}

### 6. Lấy chi tiết sản phẩm
GET {{baseUrl}}/user/products/1
Authorization: Bearer {{token}}

### 7. Cập nhật sản phẩm vật lý
PUT {{baseUrl}}/user/products/1
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "name": "Áo thun nam cao cấp - Cập nhật",
  "description": "Mô tả đã được cập nhật",
  "price": {
    "listPrice": 350000,
    "salePrice": 300000,
    "currency": "VND"
  },
  "inventory": [
    {
      "warehouseId": 1,
      "availableQuantity": 150,
      "sku": "SHIRT-001-UPDATED",
      "barcode": "1234567890125"
    }
  ],
  "tags": [
    "áo thun",
    "nam",
    "cotton",
    "updated"
  ]
}

### 8. Lấy thông tin tồn kho của sản phẩm
GET {{baseUrl}}/user/products/1/inventory
Authorization: Bearer {{token}}

### 9. Tạo/cập nhật tồn kho cho sản phẩm
POST {{baseUrl}}/user/products/1/inventory
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "warehouseId": 1,
  "availableQuantity": 200,
  "sku": "NEW-SKU-001",
  "barcode": "9876543210123"
}

### 10. Xóa sản phẩm
DELETE {{baseUrl}}/user/products/1
Authorization: Bearer {{token}}

### 11. Xóa nhiều sản phẩm
DELETE {{baseUrl}}/user/products/bulk
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "productIds": [1, 2, 3]
}

### 12. Lấy danh sách kho
GET {{baseUrl}}/user/products/warehouses
Authorization: Bearer {{token}}

### 13. Tạo sản phẩm vật lý với classifications (TEST LƯU DATABASE)
POST {{baseUrl}}/user/products
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "productType": "PHYSICAL",
  "name": "Áo thun test classifications",
  "description": "Test lưu classifications vào database",
  "price": {
    "listPrice": 300000,
    "salePrice": 250000,
    "currency": "VND"
  },
  "inventory": [
    {
      "warehouseId": 1,
      "availableQuantity": 100,
      "sku": "TEST-CLASSIFICATION-001"
    }
  ],
  "classifications": [
    {
      "type": "Đen - Size M",
      "price": {
        "listPrice": 250000,
        "salePrice": 199000,
        "currency": "VND"
      },
      "description": "Màu đen size M",
      "customFields": [
        {
          "customFieldId": 127,
          "value": {
            "value": true
          }
        }
      ],
      "imagesMediaTypes": ["image/jpeg", "image/jpeg"]
    },
    {
      "type": "Trắng - Size L",
      "price": {
        "listPrice": 270000,
        "salePrice": 220000,
        "currency": "VND"
      },
      "description": "Màu trắng size L",
      "imagesMediaTypes": ["image/png"]
    }
  ]
}

### 14. Tạo nhiều sản phẩm vật lý cùng lúc (Batch)
POST {{baseUrl}}/user/products/batch
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "products": [
    {
      "productType": "PHYSICAL",
      "name": "Áo thun batch 1",
      "price": {
        "listPrice": 200000,
        "salePrice": 180000,
        "currency": "VND"
      },
      "inventory": [
        {
          "warehouseId": 1,
          "availableQuantity": 50,
          "sku": "BATCH-001"
        }
      ]
    },
    {
      "productType": "PHYSICAL",
      "name": "Áo thun batch 2",
      "price": {
        "listPrice": 220000,
        "salePrice": 200000,
        "currency": "VND"
      },
      "inventory": [
        {
          "warehouseId": 1,
          "availableQuantity": 30,
          "sku": "BATCH-002"
        }
      ]
    }
  ]
}

### ===== DOCUMENTATION =====

### Cấu trúc DTO cho sản phẩm vật lý (PhysicalProductCreateDto):

### TRƯỜNG BẮT BUỘC:
# - productType: "PHYSICAL" (luôn luôn)
# - name: string (tên sản phẩm, max 255 ký tự)
# - price: object (thông tin giá - bắt buộc cho sản phẩm vật lý)
# - inventory: array (ít nhất 1 phần tử - BẮT BUỘC cho sản phẩm vật lý)

### TRƯỜNG TÙY CHỌN:
# - description: string (mô tả sản phẩm)
# - imagesMediaTypes: string[] (loại hình ảnh: "image/jpeg", "image/png")
# - tags: string[] (danh sách tags)
# - customFields: CustomFieldInputDto[] (custom fields)
# - shipmentConfig: object (cấu hình vận chuyển)
# - classifications: CreateClassificationDto[] (phân loại sản phẩm)

### CẤU TRÚC PRICE (ProductPriceDto):
# Loại 1 - HasPriceDto:
# {
#   "listPrice": number,
#   "salePrice": number,
#   "currency": string
# }
#
# Loại 2 - StringPriceDto:
# {
#   "priceDescription": string
# }

### CẤU TRÚC INVENTORY (ProductInventoryDto[]):
# Cách 1 - Sử dụng inventory có sẵn:
# {
#   "inventoryId": number
# }
#
# Cách 2 - Tạo inventory mới:
# {
#   "warehouseId": number,
#   "availableQuantity": number,
#   "sku": string (optional, max 100 ký tự),
#   "barcode": string (optional, max 100 ký tự)
# }

### CẤU TRÚC SHIPMENT CONFIG:
# {
#   "widthCm": number (0-1000),
#   "heightCm": number (0-1000),
#   "lengthCm": number (0-1000),
#   "weightGram": number (0-100000)
# }

### CẤU TRÚC CUSTOM FIELDS:
# {
#   "customFieldId": number,
#   "value": {
#     "value": any
#   }
# }

### CẤU TRÚC CLASSIFICATIONS:
# {
#   "type": string,
#   "price": ProductPriceDto,
#   "description": string (optional),
#   "customFields": CustomFieldInputDto[] (optional),
#   "imagesMediaTypes": string[] (optional)
# }

### LỖI THƯỜNG GẶP:
# - Code 30206: "At least one inventory is required for physical products"
#   => Nguyên nhân: Thiếu trường inventory hoặc inventory là array rỗng
#   => Giải pháp: Đảm bảo inventory là array có ít nhất 1 phần tử
#
# - Classifications không được lưu vào database
#   => Nguyên nhân: Method processClassifications đã được implement để lưu vào bảng user_classifications
#   => Giải pháp: Classifications sẽ được lưu và trả về presigned URLs cho upload ảnh

### ENDPOINT URLS:
# - POST /user/products - Tạo sản phẩm mới (bao gồm classifications)
# - POST /user/products/batch - Tạo nhiều sản phẩm
# - GET /user/products - Lấy danh sách sản phẩm
# - GET /user/products/:id - Lấy chi tiết sản phẩm
# - PUT /user/products/:id - Cập nhật sản phẩm
# - DELETE /user/products/:id - Xóa sản phẩm
# - DELETE /user/products/bulk - Xóa nhiều sản phẩm
# - GET /user/products/warehouses - Lấy danh sách kho
# - GET /user/products/:id/inventory - Lấy tồn kho sản phẩm
# - POST /user/products/:id/inventory - Tạo/cập nhật tồn kho
#
### CLASSIFICATIONS ĐƯỢC LƯU VÀO DATABASE:
# - Bảng: user_classifications
# - Các trường: id, type, price (jsonb), product_id, metadata (jsonb),
#   custom_fields (jsonb), images_media (jsonb), description, sku
# - Response bao gồm: classifications array + classificationUploadUrls array
# - imagesMediaTypes trong classifications tạo presigned URLs riêng
