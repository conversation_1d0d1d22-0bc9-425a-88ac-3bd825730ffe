import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsNumber,
  IsString,
  IsEnum,
  IsOptional,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';
import { AffiliateAccountStatus } from './affiliate-account-query.dto';

/**
 * DTO cho thông tin người dùng trong tài khoản affiliate
 */
export class AffiliateAccountUserDto {
  @ApiProperty({
    description: 'ID của người dùng',
    example: 123,
  })
  @IsNumber()
  id: number;

  @ApiProperty({
    description: 'Họ tên người dùng',
    example: 'Nguyễn Văn A',
  })
  @IsString()
  fullName: string;

  @ApiProperty({
    description: 'Email người dùng',
    example: '<EMAIL>',
  })
  @IsString()
  email: string;

  @ApiPropertyOptional({
    description: 'Số điện thoại người dùng',
    example: '**********',
  })
  @IsOptional()
  @IsString()
  phoneNumber?: string;
}

/**
 * DTO cho thông tin rank trong tài khoản affiliate
 */
export class AffiliateAccountRankDto {
  @ApiProperty({
    description: 'ID của rank',
    example: 2,
  })
  @IsNumber()
  id: number;

  @ApiProperty({
    description: 'Tên rank',
    example: 'Silver',
  })
  @IsString()
  rankName: string;

  @ApiProperty({
    description: 'Icon rank',
    example: 'silver_badge.png',
  })
  @IsString()
  rankBadge: string;

  @ApiProperty({
    description: 'Mức hoa hồng rank',
    example: 5.5,
  })
  @IsNumber()
  commission: number;
}

/**
 * DTO cho thông tin tài khoản affiliate
 */
export class AffiliateAccountDto {
  @ApiProperty({
    description: 'ID của tài khoản affiliate',
    example: 123,
  })
  @IsNumber()
  id: number;

  @ApiProperty({
    description: 'Thông tin người dùng',
    type: AffiliateAccountUserDto,
  })
  @ValidateNested()
  @Type(() => AffiliateAccountUserDto)
  user: AffiliateAccountUserDto;

  @ApiPropertyOptional({
    description: 'Thông tin rank',
    type: AffiliateAccountRankDto,
    nullable: true,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => AffiliateAccountRankDto)
  rank: AffiliateAccountRankDto | null;

  @ApiProperty({
    description: 'Loại tài khoản',
    example: 'PERSONAL',
  })
  @IsString()
  accountType: string;

  @ApiProperty({
    description: 'Trạng thái tài khoản',
    enum: AffiliateAccountStatus,
    example: AffiliateAccountStatus.ACTIVE,
  })
  @IsEnum(AffiliateAccountStatus)
  status: AffiliateAccountStatus;

  @ApiProperty({
    description: 'Số dư khả dụng',
    example: 1500000,
  })
  @IsNumber()
  availableBalance: number;

  @ApiProperty({
    description: 'Tổng thu nhập',
    example: 3500000,
  })
  @IsNumber()
  totalEarnings: number;

  @ApiProperty({
    description: 'Giá trị performance',
    example: 25,
  })
  @IsNumber()
  performance: number;

  @ApiProperty({
    description: 'Mã giới thiệu',
    example: 'NGUYENA123',
  })
  @IsString()
  referralCode: string;

  @ApiProperty({
    description: 'Thời gian tạo tài khoản (Unix timestamp)',
    example: **********,
  })
  @IsNumber()
  createdAt: number;

  @ApiPropertyOptional({
    description: 'Thời gian cập nhật tài khoản (Unix timestamp)',
    example: **********,
  })
  @IsOptional()
  @IsNumber()
  updatedAt?: number;
}
