import { Body, Controller, Post, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { JwtUserGuard } from '@modules/auth/guards/jwt-user.guard';
import { CurrentUser } from '@modules/auth/decorators/current-user.decorator';
import { JWTPayload } from '@modules/auth/interfaces/jwt-payload.interface';
import { ApiResponseDto } from '@/common/response';
import { AffiliateUploadService } from '../../services/affiliate-upload.service';
import { AffiliateRegistrationService } from '../../state-machine/affiliate-registration.service';
import { BusinessLicenseUploadUrlDto, SignedContractUploadUrlDto } from '../../dto';
import { SWAGGER_API_TAGS } from '@/common/swagger';

@ApiTags(SWAGGER_API_TAGS.USER_AFFILIATE_UPLOAD)
@Controller('affiliate/upload')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
export class AffiliateUploadController {
  constructor(
    private readonly affiliateUploadService: AffiliateUploadService,
    private readonly affiliateRegistrationService: AffiliateRegistrationService,
  ) {}

  @Post('business-license/url')
  @ApiOperation({ summary: 'Tạo URL tạm thời để upload giấy phép kinh doanh' })
  @ApiResponse({
    status: 200,
    description: 'Tạo URL upload thành công',
    type: ApiResponseDto,
  })
  async getBusinessLicenseUploadUrl(
    @CurrentUser() user: JWTPayload,
    @Body() dto: BusinessLicenseUploadUrlDto,
  ): Promise<ApiResponseDto<{ uploadUrl: string; key: string }>> {
    const result = await this.affiliateUploadService.createBusinessLicenseUploadUrl(user.id, dto.mediaType);
    return ApiResponseDto.success(result, 'Tạo URL upload giấy phép kinh doanh thành công');
  }

  @Post('business-license/confirm')
  @ApiOperation({ summary: 'Xác nhận đã upload giấy phép kinh doanh' })
  @ApiResponse({
    status: 200,
    description: 'Xác nhận upload thành công',
    type: ApiResponseDto,
  })
  async confirmBusinessLicenseUpload(
    @CurrentUser() user: JWTPayload,
    @Body() body: { businessLicenseUrl: string },
  ): Promise<ApiResponseDto<{ success: boolean }>> {
    // Gửi sự kiện để cập nhật trạng thái
    const success = this.affiliateRegistrationService.sendEvent(
      user.id,
      'UPLOAD_BUSINESS_LICENSE',
      { businessLicenseUrl: body.businessLicenseUrl },
    );

    return ApiResponseDto.success({ success }, 'Xác nhận upload giấy phép kinh doanh thành công');
  }

  @Post('signed-contract/url')
  @ApiOperation({ summary: 'Tạo URL tạm thời để upload hợp đồng đã ký' })
  @ApiResponse({
    status: 200,
    description: 'Tạo URL upload thành công',
    type: ApiResponseDto,
  })
  async getSignedContractUploadUrl(
    @CurrentUser() user: JWTPayload,
    @Body() dto: SignedContractUploadUrlDto,
  ): Promise<ApiResponseDto<{ uploadUrl: string; key: string }>> {
    const result = await this.affiliateUploadService.createSignedContractUploadUrl(user.id, dto.mediaType);
    return ApiResponseDto.success(result, 'Tạo URL upload hợp đồng đã ký thành công');
  }

  @Post('signed-contract/confirm')
  @ApiOperation({ summary: 'Xác nhận đã upload hợp đồng đã ký' })
  @ApiResponse({
    status: 200,
    description: 'Xác nhận upload thành công',
    type: ApiResponseDto,
  })
  async confirmSignedContractUpload(
    @CurrentUser() user: JWTPayload,
    @Body() body: { signedContractUrl: string },
  ): Promise<ApiResponseDto<{ success: boolean }>> {
    // Gửi sự kiện để cập nhật trạng thái
    const success = this.affiliateRegistrationService.sendEvent(
      user.id,
      'UPLOAD_SIGNED_CONTRACT',
      { signedContractUrl: body.signedContractUrl },
    );

    return ApiResponseDto.success({ success }, 'Xác nhận upload hợp đồng đã ký thành công');
  }
}
