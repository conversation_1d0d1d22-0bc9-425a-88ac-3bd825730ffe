import { Body, Controller, Post, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiProperty, ApiResponse, ApiTags } from '@nestjs/swagger';
import { JwtUserGuard } from '@modules/auth/guards/jwt-user.guard';
import { CurrentUser } from '@modules/auth/decorators/current-user.decorator';
import { JWTPayload } from '@modules/auth/interfaces/jwt-payload.interface';
import { ApiResponseDto } from '@/common/response';
import { CitizenIdUploadService } from '../../services/citizen-id-upload.service';
import { AffiliateRegistrationService } from '../../state-machine/affiliate-registration.service';
import { IsNotEmpty, IsString } from 'class-validator';
import { SignatureBase64Dto } from '../../dto/signature-base64.dto';
import { SWAGGER_API_TAGS } from '@/common/swagger';

class CitizenIdUploadDto {
  @ApiProperty({
    description: 'URL ảnh mặt trước CCCD',
    example: 'affiliate/123/citizen-id/front-1234567890.jpg',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  citizenIdFrontUrl: string;

  @ApiProperty({
    description: 'URL ảnh mặt sau CCCD',
    example: 'affiliate/123/citizen-id/back-1234567890.jpg',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  citizenIdBackUrl: string;
}

class SignatureUploadDto {
  @ApiProperty({
    description: 'URL chữ ký',
    example: 'affiliate/123/signature/1234567890.png',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  signatureUrl: string;
}

@ApiTags(SWAGGER_API_TAGS.USER_AFFILIATE_UPLOAD)
@Controller('affiliate/upload')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
export class CitizenIdUploadController {
  constructor(
    private readonly citizenIdUploadService: CitizenIdUploadService,
    private readonly affiliateRegistrationService: AffiliateRegistrationService,
  ) {}

  @Post('citizen-id/front/url')
  @ApiOperation({ summary: 'Tạo URL tạm thời để upload ảnh mặt trước CCCD' })
  @ApiResponse({
    status: 200,
    description: 'Tạo URL upload thành công',
    type: ApiResponseDto,
  })
  async getCitizenIdFrontUploadUrl(
    @CurrentUser() user: JWTPayload,
  ): Promise<ApiResponseDto<{ uploadUrl: string; key: string }>> {
    const result = await this.citizenIdUploadService.createCitizenIdFrontUploadUrl(user.id);
    return ApiResponseDto.success(result, 'Tạo URL upload ảnh mặt trước CCCD thành công');
  }

  @Post('citizen-id/back/url')
  @ApiOperation({ summary: 'Tạo URL tạm thời để upload ảnh mặt sau CCCD' })
  @ApiResponse({
    status: 200,
    description: 'Tạo URL upload thành công',
    type: ApiResponseDto,
  })
  async getCitizenIdBackUploadUrl(
    @CurrentUser() user: JWTPayload,
  ): Promise<ApiResponseDto<{ uploadUrl: string; key: string }>> {
    const result = await this.citizenIdUploadService.createCitizenIdBackUploadUrl(user.id);
    return ApiResponseDto.success(result, 'Tạo URL upload ảnh mặt sau CCCD thành công');
  }

  @Post('citizen-id/confirm')
  @ApiOperation({ summary: 'Xác nhận đã upload ảnh CCCD' })
  @ApiResponse({
    status: 200,
    description: 'Xác nhận upload thành công',
    type: ApiResponseDto,
  })
  async confirmCitizenIdUpload(
    @CurrentUser() user: JWTPayload,
    @Body() body: CitizenIdUploadDto,
  ): Promise<ApiResponseDto<{ success: boolean }>> {
    // Gửi sự kiện để cập nhật trạng thái
    const success = this.affiliateRegistrationService.sendEvent(
      user.id,
      'UPLOAD_CITIZEN_ID',
      {
        citizenIdFrontUrl: body.citizenIdFrontUrl,
        citizenIdBackUrl: body.citizenIdBackUrl,
      },
    );

    return ApiResponseDto.success({ success }, 'Xác nhận upload ảnh CCCD thành công');
  }

  @Post('signature/base64')
  @ApiOperation({ summary: 'Upload chữ ký dưới dạng base64' })
  @ApiResponse({
    status: 200,
    description: 'Upload chữ ký thành công',
    type: ApiResponseDto,
  })
  async uploadSignatureBase64(
    @CurrentUser() user: JWTPayload,
    @Body() dto: SignatureBase64Dto,
  ): Promise<ApiResponseDto<{ key: string }>> {
    const key = await this.citizenIdUploadService.saveSignatureBase64(user.id, dto.signatureBase64);
    return ApiResponseDto.success({ key }, 'Upload chữ ký thành công');
  }

  @Post('signature/confirm')
  @ApiOperation({ summary: 'Xác nhận đã upload chữ ký' })
  @ApiResponse({
    status: 200,
    description: 'Xác nhận upload thành công',
    type: ApiResponseDto,
  })
  async confirmSignatureUpload(
    @CurrentUser() user: JWTPayload,
    @Body() body: SignatureUploadDto,
  ): Promise<ApiResponseDto<{ success: boolean }>> {
    // Gửi sự kiện để cập nhật trạng thái
    const success = this.affiliateRegistrationService.sendEvent(
      user.id,
      'SUBMIT_SIGNATURE',
      { signatureUrl: body.signatureUrl },
    );

    return ApiResponseDto.success({ success }, 'Xác nhận upload chữ ký thành công');
  }
}
