import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AgentAdminModule } from './admin/agent-admin.module';
import { AgentUserModule } from './user/agent-user.module';
import * as entities from './entities';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      entities.Agent,
      entities.AgentSystem,
      entities.AgentUser,
      entities.AgentTemplate,
      entities.AgentUrl,
      entities.AgentMedia,
      entities.AgentProduct,
      entities.TypeAgent,
      entities.AgentRank,
      entities.AgentStrategy,
      entities.AgentStrategyUser,

      // MCP System Entities
      entities.McpSystems,
      entities.AgentSystemMcp,

      // Type Agent System Entities
      entities.TypeAgentAgentSystem,

      // Rank Strategy Entities
      entities.RankStrategy
    ]),
    AgentAdminModule,
    AgentUserModule
  ],
  providers: [],
  exports: [
    AgentAdminModule,
    AgentUserModule,
    TypeOrmModule
  ],
})
export class AgentModule {}
