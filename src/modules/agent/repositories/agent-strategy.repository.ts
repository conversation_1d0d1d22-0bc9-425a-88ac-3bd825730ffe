import { Injectable, Logger } from '@nestjs/common';
import { DataSource, Repository, SelectQueryBuilder } from 'typeorm';
import { Transactional } from 'typeorm-transactional';
import { AgentStrategy } from '@modules/agent/entities/agents-strategy.entity';
import { Agent } from '@modules/agent/entities/agent.entity';
import { PaginatedResult } from '@common/response';
import { AppException } from '@common/exceptions';
import { AGENT_ERROR_CODES } from '@modules/agent/exceptions';
import { QueryAgentStrategyDto } from '@modules/agent/admin/dto/agent-strategy';

/**
 * Repository cho AgentStrategy
 * Xử lý các thao tác CRUD và truy vấn nâng cao liên quan đến chiến lược agent
 */
@Injectable()
export class AgentStrategyRepository extends Repository<AgentStrategy> {
  private readonly logger = new Logger(AgentStrategyRepository.name);

  constructor(private dataSource: DataSource) {
    super(AgentStrategy, dataSource.createEntityManager());
  }

  /**
   * Tạo query builder c<PERSON> bản cho AgentStrategy
   * @returns SelectQueryBuilder cho AgentStrategy
   */
  createBaseQuery(): SelectQueryBuilder<AgentStrategy> {
    return this.createQueryBuilder('agentStrategy').select([
      'agentStrategy.id',
      'agentStrategy.content',
      'agentStrategy.exampleDefault',
      'agentStrategy.systemModelId',
      'agentStrategy.createdBy',
      'agentStrategy.updatedBy',
      'agentStrategy.deletedBy',
    ]);
  }

  /**
   * Tạo query builder cơ bản với điều kiện soft delete
   * @returns SelectQueryBuilder cho AgentStrategy chưa bị xóa
   */
  createBaseQueryWithSoftDelete(): SelectQueryBuilder<AgentStrategy> {
    return this.createBaseQuery().where('agentStrategy.deletedBy IS NULL');
  }

  /**
   * Tìm chiến lược agent theo ID với thông tin agent (alias cho findById)
   * @param id ID của chiến lược agent
   * @returns Object chứa agent và strategy hoặc null
   */
  async findByIdWithAgent(id: string): Promise<{
    agent: Agent;
    strategy: AgentStrategy;
    modelId?: string | undefined;
    modelSystemId?: string | undefined;
    createdByEmployee?: {
      employeeId: number;
      name: string;
      avatar: string | null;
    };
    updatedByEmployee?: {
      employeeId: number;
      name: string;
      avatar: string | null;
    };
  } | null> {
    return this.findById(id);
  }

  /**
   * Tìm chiến lược agent theo ID với thông tin agent
   * @param id ID của chiến lược agent
   * @returns Object chứa agent và strategy hoặc null
   */
  async findById(id: string): Promise<{
    agent: Agent;
    strategy: AgentStrategy;
    modelId?: string;
    modelSystemId?: string;
    createdByEmployee?: {
      employeeId: number;
      name: string;
      avatar: string | null;
    };
    updatedByEmployee?: {
      employeeId: number;
      name: string;
      avatar: string | null;
    };
  } | null> {
    try {
      // Kiểm tra xem record có tồn tại trong agents_strategy không
      const strategyExists = await this.createQueryBuilder('agentStrategy')
        .where('agentStrategy.id = :id', { id })
        .getOne();

      this.logger.debug(
        `Strategy exists check for ${id}:`,
        strategyExists ? 'Found' : 'Not found',
      );

      if (strategyExists) {
        this.logger.debug(`Strategy data:`, {
          id: strategyExists.id,
          content: strategyExists.content,
          exampleDefault: strategyExists.exampleDefault,
          systemModelId: strategyExists.systemModelId,
          createdBy: strategyExists.createdBy,
          updatedBy: strategyExists.updatedBy,
        });
      }
      const rawResult = await this.createQueryBuilder('agentStrategy')
        .leftJoinAndSelect('agents', 'agent', 'agent.id = agentStrategy.id')
        .leftJoin(
          'system_models',
          'systemModel',
          'agentStrategy.system_model_id = systemModel.id',
        )
        .leftJoin(
          'employees',
          'createdEmployee',
          'agentStrategy.created_by = createdEmployee.id',
        )
        .leftJoin(
          'employees',
          'updatedEmployee',
          'agentStrategy.updated_by = updatedEmployee.id',
        )
        .select([
          'agentStrategy.id AS agentStrategy_id',
          'agentStrategy.content AS agent_strategy_content',
          'agentStrategy.example_default AS agent_strategy_example_default',
          'agentStrategy.system_model_id AS agent_strategy_system_model_id',
          'agentStrategy.created_by AS agentStrategy_createdBy',
          'agentStrategy.updated_by AS agentStrategy_updatedBy',
          'agentStrategy.deleted_by AS agentStrategy_deletedBy',
          'agent.id AS agent_id',
          'agent.name AS agent_name',
          'agent.avatar AS agent_avatar',
          'agent.model_config AS agent_modelConfig',
          'agent.instruction AS agent_instruction',
          'agent.vector_store_id AS agent_vectorStoreId',
          'agent.created_at AS agent_createdAt',
          'agent.updated_at AS agent_updatedAt',
          'agent.deleted_at AS agent_deletedAt',
          'systemModel.model_id AS system_model_model_id',
          'createdEmployee.name AS created_employee_name',
          'createdEmployee.avatar AS created_employee_avatar',
          'updatedEmployee.name AS updated_employee_name',
          'updatedEmployee.avatar AS updated_employee_avatar',
        ])
        .where('agentStrategy.id = :id', { id })
        .andWhere('agentStrategy.deleted_by IS NULL')
        .andWhere('agent.deleted_at IS NULL')
        .getRawOne();

      if (!rawResult) {
        return null;
      }

      return {
        agent: {
          id: rawResult.agent_id,
          name: rawResult.agent_name,
          avatar: rawResult.agent_avatar,
          modelConfig: rawResult.agent_modelConfig,
          instruction: rawResult.agent_instruction,
          vectorStoreId: rawResult.agent_vectorStoreId,
          createdAt: rawResult.agent_createdAt,
          updatedAt: rawResult.agent_updatedAt,
          deletedAt: rawResult.agent_deletedAt,
        } as Agent,
        strategy: {
          id: rawResult.agentStrategy_id,
          content: rawResult.agent_strategy_content || [],
          exampleDefault: rawResult.agent_strategy_example_default || [],
          systemModelId: rawResult.agentStrategy_systemModelId,
          createdBy: rawResult.agentStrategy_createdBy,
          updatedBy: rawResult.agentStrategy_updatedBy,
          deletedBy: rawResult.agentStrategy_deletedBy,
        } as AgentStrategy,
        modelId: rawResult.system_model_model_id || '',
        modelSystemId: rawResult.agent_strategy_system_model_id || '',
        createdByEmployee: rawResult.agentStrategy_createdBy ? {
          employeeId: rawResult.agentStrategy_createdBy,
          name: rawResult.created_employee_name || '',
          avatar: rawResult.created_employee_avatar || null,
        } : undefined,
        updatedByEmployee: rawResult.agentStrategy_updatedBy ? {
          employeeId: rawResult.agentStrategy_updatedBy,
          name: rawResult.updated_employee_name || '',
          avatar: rawResult.updated_employee_avatar || null,
        } : undefined,
      };
    } catch (error) {
      this.logger.error(
        `Lỗi khi tìm chiến lược agent theo ID ${id}: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        AGENT_ERROR_CODES.STRATEGY_NOT_FOUND,
        'Không thể tìm chiến lược agent',
      );
    }
  }

  /**
   * Tìm agent và chiến lược đã bị xóa theo ID
   * @param id ID của agent và chiến lược
   * @returns Object chứa agent và strategy hoặc null
   */
  async findDeletedById(
    id: string,
  ): Promise<{
    agent: Agent;
    strategy: AgentStrategy;
    modelId?: string;
  } | null> {
    try {
      const rawResult = await this.createQueryBuilder('agentStrategy')
        .leftJoinAndSelect('agents', 'agent', 'agent.id = agentStrategy.id')
        .leftJoin(
          'system_models',
          'systemModel',
          'agentStrategy.systemModelId = systemModel.id',
        )
        .select([
          'agentStrategy.id',
          'agentStrategy.content',
          'agentStrategy.exampleDefault',
          'agentStrategy.systemModelId',
          'agentStrategy.createdBy',
          'agentStrategy.updatedBy',
          'agentStrategy.deletedBy',
          'agent.id',
          'agent.name',
          'agent.avatar',
          'agent.modelConfig',
          'agent.instruction',
          'agent.vectorStoreId',
          'agent.createdAt',
          'agent.updatedAt',
          'agent.deletedAt',
          'systemModel.model_id AS systemModel_model_id',
        ])
        .where('agentStrategy.id = :id', { id })
        .andWhere('agentStrategy.deletedBy IS NOT NULL')
        .getRawOne();

      if (!rawResult) {
        return null;
      }

      return {
        agent: {
          id: rawResult.agent_id,
          name: rawResult.agent_name,
          avatar: rawResult.agent_avatar,
          modelConfig: rawResult.agent_modelConfig,
          instruction: rawResult.agent_instruction,
          vectorStoreId: rawResult.agent_vectorStoreId,
          createdAt: rawResult.agent_createdAt,
          updatedAt: rawResult.agent_updatedAt,
          deletedAt: rawResult.agent_deletedAt,
        } as Agent,
        strategy: {
          id: rawResult.agentStrategy_id,
          content: rawResult.agentStrategy_content,
          exampleDefault: rawResult.agentStrategy_exampleDefault,
          systemModelId: rawResult.agentStrategy_systemModelId,
          createdBy: rawResult.agentStrategy_createdBy,
          updatedBy: rawResult.agentStrategy_updatedBy,
          deletedBy: rawResult.agentStrategy_deletedBy,
        } as AgentStrategy,
        modelId: rawResult.systemModel_model_id || null,
      };
    } catch (error) {
      this.logger.error(
        `Lỗi khi tìm agent và chiến lược đã xóa theo ID ${id}: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        AGENT_ERROR_CODES.STRATEGY_NOT_FOUND,
        'Không thể tìm agent và chiến lược đã xóa',
      );
    }
  }

  /**
   * Lấy danh sách chiến lược agent có phân trang với thông tin agent
   * @param queryDto Tham số truy vấn
   * @returns Danh sách chiến lược agent có phân trang với thông tin agent
   */
  async findPaginated(queryDto: QueryAgentStrategyDto): Promise<
    PaginatedResult<{
      agent: Agent;
      strategy: AgentStrategy;
      modelId?: string;
    }>
  > {
    try {
      const { page, limit, search, sortBy, sortDirection } = queryDto;

      const queryBuilder = this.createQueryBuilder('agentStrategy')
        .leftJoinAndSelect('agents', 'agent', 'agent.id = agentStrategy.id')
        .leftJoin(
          'system_models',
          'systemModel',
          'agentStrategy.systemModelId = systemModel.id',
        )
        .select([
          'agentStrategy.id',
          'agentStrategy.content',
          'agentStrategy.exampleDefault',
          'agentStrategy.systemModelId',
          'agentStrategy.createdBy',
          'agentStrategy.updatedBy',
          'agentStrategy.deletedBy',
          'agent.id',
          'agent.name',
          'agent.avatar',
          'agent.modelConfig',
          'agent.instruction',
          'agent.vectorStoreId',
          'agent.createdAt',
          'agent.updatedAt',
          'agent.deletedAt',
          'systemModel.model_id AS system_model_id',
        ])
        .where('agentStrategy.deletedBy IS NULL')
        .andWhere('agent.deletedAt IS NULL');

      // Tìm kiếm theo từ khóa
      if (search) {
        queryBuilder.andWhere(
          '("agent"."name" ILIKE :search OR "systemModel"."model_id" ILIKE :search)',
          { search: `%${search}%` },
        );
      }

      // Sắp xếp - AgentStrategy không có created_at, dùng agent.created_at
      const sortColumn =
        sortBy === 'createdAt' ? '"agent"."created_at"' : '"agent"."name"';
      const direction = sortDirection?.toUpperCase() === 'ASC' ? 'ASC' : 'DESC';
      queryBuilder.orderBy(sortColumn, direction as 'ASC' | 'DESC');

      // Phân trang
      const skip = (page - 1) * limit;
      queryBuilder.offset(skip).limit(limit);

      // Thực hiện truy vấn
      const [rawResults, totalItems] = await Promise.all([
        queryBuilder.getRawMany(),
        queryBuilder.getCount(),
      ]);

      // Transform raw results to proper format
      const items = rawResults.map((raw) => {
        // Debug logging
        this.logger.debug(
          `Raw result for agent ${raw.agent_id}: systemModelId=${raw.agentStrategy_systemModelId}, modelId=${raw.systemModel_model_id}`,
        );

        return {
          agent: {
            id: raw.agent_id,
            name: raw.agent_name,
            avatar: raw.agent_avatar,
            modelConfig: raw.agent_modelConfig,
            instruction: raw.agent_instruction,
            vectorStoreId: raw.agent_vectorStoreId,
            createdAt: raw.agent_createdAt,
            updatedAt: raw.agent_updatedAt,
            deletedAt: raw.agent_deletedAt,
          } as Agent,
          strategy: {
            id: raw.agentStrategy_id,
            content: raw.agentStrategy_content,
            exampleDefault: raw.agentStrategy_exampleDefault,
            systemModelId: raw.agentStrategy_systemModelId,
            createdBy: raw.agentStrategy_createdBy,
            updatedBy: raw.agentStrategy_updatedBy,
            deletedBy: raw.agentStrategy_deletedBy,
          } as AgentStrategy,
          modelId: raw.system_model_id || null,
        };
      });

      return {
        items,
        meta: {
          totalItems,
          itemCount: items.length,
          itemsPerPage: limit,
          totalPages: Math.ceil(totalItems / limit),
          currentPage: page,
          hasItems: totalItems > 0,
        },
      };
    } catch (error) {
      this.logger.error(
        `Lỗi khi lấy danh sách chiến lược agent: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        AGENT_ERROR_CODES.STRATEGY_NOT_FOUND,
        'Không thể lấy danh sách chiến lược agent',
      );
    }
  }

  /**
   * Lấy danh sách chiến lược agent đã xóa có phân trang với thông tin agent
   * @param queryDto Tham số truy vấn
   * @returns Danh sách chiến lược agent đã xóa có phân trang với thông tin agent
   */
  async findDeletedPaginated(queryDto: QueryAgentStrategyDto): Promise<
    PaginatedResult<{
      agent: Agent;
      strategy: AgentStrategy;
      modelId?: string;
    }>
  > {
    try {
      const { page, limit, search, sortBy, sortDirection } = queryDto;

      const queryBuilder = this.createQueryBuilder('agentStrategy')
        .leftJoinAndSelect('agents', 'agent', 'agent.id = agentStrategy.id')
        .leftJoin(
          'system_models',
          'systemModel',
          'agentStrategy.systemModelId = systemModel.id',
        )
        .select([
          'agentStrategy.id',
          'agentStrategy.content',
          'agentStrategy.exampleDefault',
          'agentStrategy.systemModelId',
          'agentStrategy.createdBy',
          'agentStrategy.updatedBy',
          'agentStrategy.deletedBy',
          'agent.id',
          'agent.name',
          'agent.avatar',
          'agent.modelConfig',
          'agent.instruction',
          'agent.vectorStoreId',
          'agent.createdAt',
          'agent.updatedAt',
          'agent.deletedAt',
          'systemModel.model_id AS model_id',
        ])
        .where('agentStrategy.deletedBy IS NOT NULL');

      // Tìm kiếm theo từ khóa
      if (search) {
        queryBuilder.andWhere(
          '("agent"."name" ILIKE :search OR "systemModel"."model_id" ILIKE :search)',
          { search: `%${search}%` },
        );
      }

      // Sắp xếp - AgentStrategy không có created_at, dùng agent.created_at
      const sortColumn =
        sortBy === 'createdAt' ? '"agent"."created_at"' : '"agent"."name"';
      const direction = sortDirection?.toUpperCase() === 'ASC' ? 'ASC' : 'DESC';
      queryBuilder.orderBy(sortColumn, direction as 'ASC' | 'DESC');

      // Phân trang
      const skip = (page - 1) * limit;
      queryBuilder.offset(skip).limit(limit);

      // Thực hiện truy vấn
      const [rawResults, totalItems] = await Promise.all([
        queryBuilder.getRawMany(),
        queryBuilder.getCount(),
      ]);

      // Transform raw results to proper format
      const items = rawResults.map((raw) => ({
        agent: {
          id: raw.agent_id,
          name: raw.agent_name,
          avatar: raw.agent_avatar,
          modelConfig: raw.agent_modelConfig,
          instruction: raw.agent_instruction,
          vectorStoreId: raw.agent_vectorStoreId,
          createdAt: raw.agent_createdAt,
          updatedAt: raw.agent_updatedAt,
          deletedAt: raw.agent_deletedAt,
        } as Agent,
        strategy: {
          id: raw.agentStrategy_id,
          content: raw.agentStrategy_content,
          exampleDefault: raw.agentStrategy_exampleDefault,
          systemModelId: raw.agentStrategy_systemModelId,
          createdBy: raw.agentStrategy_createdBy,
          updatedBy: raw.agentStrategy_updatedBy,
          deletedBy: raw.agentStrategy_deletedBy,
        } as AgentStrategy,
        modelId: raw.model_id || null,
      }));

      return {
        items,
        meta: {
          totalItems,
          itemCount: items.length,
          itemsPerPage: limit,
          totalPages: Math.ceil(totalItems / limit),
          currentPage: page,
          hasItems: totalItems > 0,
        },
      };
    } catch (error) {
      this.logger.error(
        `Lỗi khi lấy danh sách chiến lược agent đã xóa: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        AGENT_ERROR_CODES.STRATEGY_NOT_FOUND,
        'Không thể lấy danh sách chiến lược agent đã xóa',
      );
    }
  }

  /**
   * Tạo chiến lược agent mới
   * @param agentStrategy Dữ liệu chiến lược agent
   * @returns AgentStrategy đã tạo
   */
  @Transactional()
  async createStrategy(
    agentStrategy: Partial<AgentStrategy>,
  ): Promise<AgentStrategy> {
    try {
      const newStrategy = this.create(agentStrategy);
      return await this.save(newStrategy);
    } catch (error) {
      this.logger.error(
        `Lỗi khi tạo chiến lược agent: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        AGENT_ERROR_CODES.STRATEGY_NOT_FOUND,
        'Không thể tạo chiến lược agent',
      );
    }
  }

  /**
   * Cập nhật chiến lược agent
   * @param id ID của chiến lược agent
   * @param updateData Dữ liệu cập nhật
   * @param updatedBy ID nhân viên cập nhật
   * @returns Kết quả cập nhật
   */
  @Transactional()
  async updateStrategy(
    id: string,
    updateData: Partial<AgentStrategy>,
    updatedBy: number,
  ): Promise<void> {
    try {
      const result = await this.createQueryBuilder()
        .update(AgentStrategy)
        .set({ ...updateData, updatedBy })
        .where('id = :id', { id })
        .andWhere('deletedBy IS NULL')
        .execute();

      if (result.affected === 0) {
        throw new AppException(
          AGENT_ERROR_CODES.STRATEGY_NOT_FOUND,
          'Không tìm thấy chiến lược agent để cập nhật',
        );
      }
    } catch (error) {
      this.logger.error(
        `Lỗi khi cập nhật chiến lược agent ${id}: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        AGENT_ERROR_CODES.STRATEGY_NOT_FOUND,
        'Không thể cập nhật chiến lược agent',
      );
    }
  }

  /**
   * Xóa mềm chiến lược agent
   * @param id ID của chiến lược agent
   * @param deletedBy ID nhân viên xóa
   * @returns Kết quả xóa
   */
  @Transactional()
  async softDeleteStrategy(id: string, deletedBy: number): Promise<void> {
    try {
      const result = await this.createQueryBuilder()
        .update(AgentStrategy)
        .set({ deletedBy })
        .where('id = :id', { id })
        .andWhere('deletedBy IS NULL')
        .execute();

      if (result.affected === 0) {
        throw new AppException(
          AGENT_ERROR_CODES.STRATEGY_NOT_FOUND,
          'Không tìm thấy chiến lược agent để xóa',
        );
      }
    } catch (error) {
      this.logger.error(
        `Lỗi khi xóa chiến lược agent ${id}: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        AGENT_ERROR_CODES.STRATEGY_NOT_FOUND,
        'Không thể xóa chiến lược agent',
      );
    }
  }

  /**
   * Khôi phục chiến lược agent đã xóa
   * @param id ID của chiến lược agent
   * @returns Kết quả khôi phục
   */
  @Transactional()
  async restoreStrategy(id: string): Promise<void> {
    try {
      const result = await this.createQueryBuilder()
        .update(AgentStrategy)
        .set({ deletedBy: null })
        .where('id = :id', { id })
        .andWhere('deletedBy IS NOT NULL')
        .execute();

      if (result.affected === 0) {
        throw new AppException(
          AGENT_ERROR_CODES.STRATEGY_NOT_FOUND,
          'Không tìm thấy chiến lược agent đã xóa để khôi phục',
        );
      }
    } catch (error) {
      this.logger.error(
        `Lỗi khi khôi phục chiến lược agent ${id}: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        AGENT_ERROR_CODES.STRATEGY_NOT_FOUND,
        'Không thể khôi phục chiến lược agent',
      );
    }
  }

  /**
   * Kiểm tra sự tồn tại của chiến lược agent
   * @param id ID của chiến lược agent
   * @returns true nếu tồn tại, false nếu không
   */
  async strategyExists(id: string): Promise<boolean> {
    try {
      const count = await this.createBaseQueryWithSoftDelete()
        .where('agentStrategy.id = :id', { id })
        .getCount();
      return count > 0;
    } catch (error) {
      this.logger.error(
        `Lỗi khi kiểm tra sự tồn tại của chiến lược agent ${id}: ${error.message}`,
        error.stack,
      );
      return false;
    }
  }
}
