import { ApiResponseDto } from '@common/response';
import { SWAGGER_API_TAGS } from '@common/swagger';
import { ApiErrorResponse } from '@common/decorators/api-error-response.decorator';
import { AgentStrategyService } from '@modules/agent/user/services/agent-strategy.service';
import { JwtUserGuard } from '@modules/auth/guards';
import { CurrentUser } from '@modules/auth/decorators/current-user.decorator';
import { AGENT_ERROR_CODES } from '@modules/agent/exceptions/agent-error-codes';
import { STRATEGY_ERROR_CODES } from '@modules/agent/exceptions/strategy-error.code';
import { ErrorCode } from '@common/exceptions';
import {
  Controller,
  UseGuards,
  Get,
  Patch,
  Param,
  Body
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiExtraModels,
  ApiTags,
  ApiOperation,
  ApiResponse
} from '@nestjs/swagger';
import {
  AgentStrategyDto,
  AssignStrategyToAgentDto
} from '../dto/agent';
/**
 * Controller xử lý các API endpoint cho Strategy của Agent người dùng
 */
@ApiTags(SWAGGER_API_TAGS.USER_AGENT)
@Controller('user/agents')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
@ApiExtraModels(
  ApiResponseDto
)
export class AgentStrategyUserController {
  constructor(private readonly agentStrategyService: AgentStrategyService) { }

  /**
   * Lấy thông tin strategy của agent
   * @param userId ID của người dùng
   * @param id ID của agent
   * @returns Thông tin strategy của agent
   */
  @Get(':id/strategy')
  @ApiOperation({ summary: 'Lấy thông tin strategy của agent' })
  @ApiResponse({
    status: 200,
    description: 'Lấy thông tin strategy thành công',
    schema: ApiResponseDto.getSchema(AgentStrategyDto),
  })
  @ApiErrorResponse(
    AGENT_ERROR_CODES.AGENT_NOT_FOUND,
    STRATEGY_ERROR_CODES.STRATEGY_NOT_FOUND,
    STRATEGY_ERROR_CODES.STRATEGY_NOT_ASSIGNED,
    STRATEGY_ERROR_CODES.STRATEGY_FETCH_FAILED,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  async getAgentStrategy(
    @CurrentUser('id') userId: number,
    @Param('id') id: string,
  ) {
    const result = await this.agentStrategyService.getAgentStrategy(id, userId);
    return ApiResponseDto.success(result, 'Lấy thông tin strategy thành công');
  }

  /**
   * Gán strategy cho agent
   * @param userId ID của người dùng
   * @param id ID của agent
   * @param dto Thông tin strategy
   * @returns Thông báo thành công
   */
  @Patch(':id/strategy')
  @ApiOperation({ summary: 'Gán strategy cho agent' })
  @ApiResponse({
    status: 200,
    description: 'Gán strategy thành công',
  })
  @ApiErrorResponse(
    AGENT_ERROR_CODES.AGENT_NOT_FOUND,
    STRATEGY_ERROR_CODES.STRATEGY_NOT_FOUND,
    STRATEGY_ERROR_CODES.STRATEGY_ASSIGN_FAILED,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  async assignStrategyToAgent(
    @CurrentUser('id') userId: number,
    @Param('id') id: string,
    @Body() dto: AssignStrategyToAgentDto,
  ) {
    await this.agentStrategyService.assignStrategyToAgent(id, userId, dto);
    return ApiResponseDto.success(null, 'Gán strategy thành công');
  }
}
