import { Test, TestingModule } from '@nestjs/testing';
import { Logger } from '@nestjs/common';
import { AgentUserService } from '../agent-user.service';
import { FacebookPageRepository } from '@modules/integration/repositories';
import { FacebookService } from '@shared/services/facebook/facebook.service';
import { AGENT_ERROR_CODES } from '@modules/agent/exceptions';
import { AppException } from '@common/exceptions';

describe('AgentUserService - Facebook Page Integration (Batch + Webhook)', () => {
  let service: AgentUserService;
  let facebookPageRepository: jest.Mocked<FacebookPageRepository>;
  let facebookService: jest.Mocked<FacebookService>;

  const mockFacebookPageRepository = {
    findPagesNotOwnedByUser: jest.fn(),
    findPagesConnectedToOtherAgents: jest.fn(),
    updateAgentIdForMultiplePages: jest.fn(),
    findByIds: jest.fn(),
  };

  const mockFacebookService = {
    subscribeApp: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        {
          provide: AgentUserService,
          useValue: {
            validateAndConnectFacebookPages: jest.fn(),
            validateFacebookPagesOwnership: jest.fn(),
            validateFacebookPagesConnection: jest.fn(),
            subscribeAppToFacebookPages: jest.fn(),
            processOutputMessengerBlock: jest.fn(),
          },
        },
        {
          provide: FacebookPageRepository,
          useValue: mockFacebookPageRepository,
        },
        {
          provide: FacebookService,
          useValue: mockFacebookService,
        },
      ],
    }).compile();

    service = module.get<AgentUserService>(AgentUserService);
    facebookPageRepository = module.get(FacebookPageRepository);
    facebookService = module.get(FacebookService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('validateAndConnectFacebookPages', () => {
    const agentId = 'agent-123';
    const userId = 1;
    const pageIds = ['page-1', 'page-2', 'page-3'];

    it('should successfully validate, connect and subscribe webhook for all Facebook pages', async () => {
      // Arrange
      facebookPageRepository.findPagesNotOwnedByUser.mockResolvedValue([]);
      facebookPageRepository.findPagesConnectedToOtherAgents.mockResolvedValue([]);
      facebookPageRepository.updateAgentIdForMultiplePages.mockResolvedValue(undefined);

      const subscribeWebhookSpy = jest.spyOn(service, 'subscribeAppToFacebookPages').mockResolvedValue(undefined);

      // Act & Assert
      await expect(
        service.validateAndConnectFacebookPages(agentId, userId, pageIds)
      ).resolves.not.toThrow();

      expect(facebookPageRepository.findPagesNotOwnedByUser).toHaveBeenCalledWith(userId, pageIds);
      expect(facebookPageRepository.findPagesConnectedToOtherAgents).toHaveBeenCalledWith(pageIds, agentId);
      expect(facebookPageRepository.updateAgentIdForMultiplePages).toHaveBeenCalledWith(pageIds, agentId);
      expect(subscribeWebhookSpy).toHaveBeenCalledWith(pageIds, agentId);
    });

    it('should throw error when some pages are not owned by user', async () => {
      // Arrange
      const notOwnedPages = ['page-2', 'page-3'];
      facebookPageRepository.findPagesNotOwnedByUser.mockResolvedValue(notOwnedPages);

      // Act & Assert
      await expect(
        service.validateAndConnectFacebookPages(agentId, userId, pageIds)
      ).rejects.toThrow(new AppException(AGENT_ERROR_CODES.FACEBOOK_PAGE_NOT_OWNED));

      expect(facebookPageRepository.findPagesNotOwnedByUser).toHaveBeenCalledWith(userId, pageIds);
      expect(facebookPageRepository.findPagesConnectedToOtherAgents).not.toHaveBeenCalled();
      expect(facebookPageRepository.updateAgentIdForMultiplePages).not.toHaveBeenCalled();
    });

    it('should throw error when some pages are already connected to other agents', async () => {
      // Arrange
      const connectedPages = [
        { pageId: 'page-1', agentId: 'other-agent-1' },
        { pageId: 'page-3', agentId: 'other-agent-2' }
      ];
      facebookPageRepository.findPagesNotOwnedByUser.mockResolvedValue([]);
      facebookPageRepository.findPagesConnectedToOtherAgents.mockResolvedValue(connectedPages);

      // Act & Assert
      await expect(
        service.validateAndConnectFacebookPages(agentId, userId, pageIds)
      ).rejects.toThrow(new AppException(AGENT_ERROR_CODES.FACEBOOK_PAGE_ALREADY_CONNECTED));

      expect(facebookPageRepository.findPagesNotOwnedByUser).toHaveBeenCalledWith(userId, pageIds);
      expect(facebookPageRepository.findPagesConnectedToOtherAgents).toHaveBeenCalledWith(pageIds, agentId);
      expect(facebookPageRepository.updateAgentIdForMultiplePages).not.toHaveBeenCalled();
    });

    it('should not execute batch update if validation fails', async () => {
      // Arrange
      facebookPageRepository.findPagesNotOwnedByUser.mockResolvedValue(['page-1']);

      // Act & Assert
      await expect(
        service.validateAndConnectFacebookPages(agentId, userId, pageIds)
      ).rejects.toThrow();

      // Verify batch update was not called
      expect(facebookPageRepository.updateAgentIdForMultiplePages).not.toHaveBeenCalled();
    });
  });

  describe('processOutputMessengerBlock', () => {
    const agentId = 'agent-123';
    const userId = 1;

    it('should process multiple Facebook page IDs using batch validation', async () => {
      // Arrange
      const outputMessenger = {
        facebookPageIds: ['page-1', 'page-2', 'page-3'],
      };

      const validateSpy = jest.spyOn(service, 'validateAndConnectFacebookPages').mockResolvedValue(undefined);

      // Act
      await service.processOutputMessengerBlock(agentId, userId, outputMessenger);

      // Assert
      expect(validateSpy).toHaveBeenCalledTimes(1);
      expect(validateSpy).toHaveBeenCalledWith(agentId, userId, ['page-1', 'page-2', 'page-3']);
    });

    it('should handle empty Facebook page IDs array', async () => {
      // Arrange
      const outputMessenger = {
        facebookPageIds: [],
      };

      const validateSpy = jest.spyOn(service, 'validateAndConnectFacebookPages');

      // Act
      await service.processOutputMessengerBlock(agentId, userId, outputMessenger);

      // Assert
      expect(validateSpy).not.toHaveBeenCalled();
    });

    it('should handle missing facebookPageIds property', async () => {
      // Arrange
      const outputMessenger = {};

      const validateSpy = jest.spyOn(service, 'validateAndConnectFacebookPages');

      // Act
      await service.processOutputMessengerBlock(agentId, userId, outputMessenger);

      // Assert
      expect(validateSpy).not.toHaveBeenCalled();
    });

    it('should handle null facebookPageIds', async () => {
      // Arrange
      const outputMessenger = {
        facebookPageIds: null,
      };

      const validateSpy = jest.spyOn(service, 'validateAndConnectFacebookPages');

      // Act
      await service.processOutputMessengerBlock(agentId, userId, outputMessenger);

      // Assert
      expect(validateSpy).not.toHaveBeenCalled();
    });
  });

  describe('Repository Integration Tests', () => {
    const agentId = 'agent-123';
    const userId = 1;
    const pageIds = ['page-1', 'page-2'];

    it('should call repository methods in correct order for validation', async () => {
      // Arrange
      facebookPageRepository.findPagesNotOwnedByUser.mockResolvedValue([]);
      facebookPageRepository.findPagesConnectedToOtherAgents.mockResolvedValue([]);
      facebookPageRepository.updateAgentIdForMultiplePages.mockResolvedValue(undefined);

      // Act
      await service.validateAndConnectFacebookPages(agentId, userId, pageIds);

      // Assert - Verify call order
      expect(facebookPageRepository.findPagesNotOwnedByUser).toHaveBeenCalledBefore(
        facebookPageRepository.findPagesConnectedToOtherAgents as jest.Mock
      );
      expect(facebookPageRepository.findPagesConnectedToOtherAgents).toHaveBeenCalledBefore(
        facebookPageRepository.updateAgentIdForMultiplePages as jest.Mock
      );
    });
  });

  describe('subscribeAppToFacebookPages', () => {
    const agentId = 'agent-123';
    const pageIds = ['page-1', 'page-2'];

    it('should successfully subscribe webhook for all pages with access tokens', async () => {
      // Arrange
      const mockPages = [
        {
          id: 'page-1',
          facebookPageId: 'fb-page-1',
          pageName: 'Page 1',
          pageAccessToken: 'token-1',
          isActive: true,
          agentId: agentId
        },
        {
          id: 'page-2',
          facebookPageId: 'fb-page-2',
          pageName: 'Page 2',
          pageAccessToken: 'token-2',
          isActive: true,
          agentId: agentId
        }
      ];

      facebookPageRepository.findByIds.mockResolvedValue(mockPages);
      facebookService.subscribeApp.mockResolvedValue({ success: true });

      // Act
      await service.subscribeAppToFacebookPages(pageIds, agentId);

      // Assert
      expect(facebookPageRepository.findByIds).toHaveBeenCalledWith(pageIds);
      expect(facebookService.subscribeApp).toHaveBeenCalledTimes(2);
      expect(facebookService.subscribeApp).toHaveBeenNthCalledWith(1, 'fb-page-1', 'token-1');
      expect(facebookService.subscribeApp).toHaveBeenNthCalledWith(2, 'fb-page-2', 'token-2');
    });

    it('should skip pages without access tokens', async () => {
      // Arrange
      const mockPages = [
        {
          id: 'page-1',
          facebookPageId: 'fb-page-1',
          pageName: 'Page 1',
          pageAccessToken: 'token-1',
          isActive: true,
          agentId: agentId
        },
        {
          id: 'page-2',
          facebookPageId: 'fb-page-2',
          pageName: 'Page 2',
          pageAccessToken: null, // No access token
          isActive: true,
          agentId: agentId
        }
      ];

      facebookPageRepository.findByIds.mockResolvedValue(mockPages);
      facebookService.subscribeApp.mockResolvedValue({ success: true });

      // Act
      await service.subscribeAppToFacebookPages(pageIds, agentId);

      // Assert
      expect(facebookService.subscribeApp).toHaveBeenCalledTimes(1);
      expect(facebookService.subscribeApp).toHaveBeenCalledWith('fb-page-1', 'token-1');
    });

    it('should handle webhook subscription failures gracefully', async () => {
      // Arrange
      const mockPages = [
        {
          id: 'page-1',
          facebookPageId: 'fb-page-1',
          pageName: 'Page 1',
          pageAccessToken: 'token-1',
          isActive: true,
          agentId: agentId
        }
      ];

      facebookPageRepository.findByIds.mockResolvedValue(mockPages);
      facebookService.subscribeApp.mockRejectedValue(new Error('Facebook API Error'));

      // Act & Assert - Should not throw error
      await expect(
        service.subscribeAppToFacebookPages(pageIds, agentId)
      ).resolves.not.toThrow();

      expect(facebookService.subscribeApp).toHaveBeenCalledWith('fb-page-1', 'token-1');
    });

    it('should handle empty page list', async () => {
      // Arrange
      facebookPageRepository.findByIds.mockResolvedValue([]);

      // Act
      await service.subscribeAppToFacebookPages([], agentId);

      // Assert
      expect(facebookPageRepository.findByIds).toHaveBeenCalledWith([]);
      expect(facebookService.subscribeApp).not.toHaveBeenCalled();
    });
  });
});
