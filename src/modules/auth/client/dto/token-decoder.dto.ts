import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString } from 'class-validator';

/**
 * DTO cho yêu cầu giải mã token
 */
export class TokenDecoderRequestDto {
  @ApiProperty({
    description: 'Token cần giải mã',
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
  })
  @IsNotEmpty({ message: 'Token không được để trống' })
  @IsString({ message: 'Token phải là chuỗi' })
  token: string;

  @ApiProperty({
    description: 'API key để xác thực yêu cầu',
    example: '1a103477-1234-4123-8a3b-23330887fa2a',
  })
  @IsNotEmpty({ message: 'API key không được để trống' })
  @IsString({ message: 'API key phải là chuỗi' })
  apiKey: string;
}

/**
 * DTO cho kết quả giải mã token
 */
export class TokenDecoderResponseDto {
  @ApiProperty({
    description: 'D<PERSON> liệu giải mã từ token',
    example: {
      userId: 1,
      email: '<EMAIL>',
      roles: ['user'],
      iat: 1619012345,
      exp: 1619098765,
    },
  })
  payload: Record<string, any>;

  @ApiProperty({
    description: 'Thời gian hết hạn của token (Unix timestamp)',
    example: 1619098765,
  })
  expiresAt: number;

  @ApiProperty({
    description: 'Thời gian còn lại của token (giây)',
    example: 3600,
  })
  expiresIn: number;

  @ApiProperty({
    description: 'Token có hợp lệ không',
    example: true,
  })
  isValid: boolean;
}
