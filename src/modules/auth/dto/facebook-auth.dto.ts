import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsNumber, IsOptional, IsString } from 'class-validator';

/**
 * DTO cho yêu cầu đăng nhập bằng Facebook OAuth2
 */
export class FacebookAuthDto {
  @ApiProperty({
    description: 'Authorization code từ Facebook OAuth2',
    example: 'AQD7veB2u5QZcM...',
  })
  @IsNotEmpty({ message: 'Authorization code không được để trống' })
  @IsString({ message: 'Authorization code phải là chuỗi' })
  code: string;

  @ApiProperty({
    description: 'URL chuyển hướng sau khi xác thực (tùy chọn)',
    example: 'https://example.com/callback',
    required: false,
  })
  @IsString({ message: 'Redirect URI phải là chuỗi' })
  redirectUri?: string;

  @ApiProperty({
    description: 'Mã người giới thiệ<PERSON> (tù<PERSON> chọn)',
    example: 12345,
    required: false,
  })
  @IsNumber({}, { message: 'Mã người giới thiệu phải là số' })
  @IsOptional()
  ref?: number;
}

/**
 * DTO cho response URL xác thực Facebook OAuth2
 */
export class FacebookAuthUrlResponseDto {
  @ApiProperty({
    description: 'URL xác thực Facebook OAuth2',
    example: 'https://www.facebook.com/v18.0/dialog/oauth?client_id=123456789&redirect_uri=https%3A%2F%2Fv2.redai.vn%2Fauth%2Fcallback&scope=email%2Cpublic_profile%2Cpages_show_list&state=facebook',
  })
  url: string;

  @ApiProperty({
    description: 'Danh sách quyền được yêu cầu',
    example: 'email,public_profile,pages_show_list',
  })
  scope: string;

  @ApiProperty({
    description: 'State parameter để bảo mật',
    example: 'facebook',
  })
  state: string;
}

