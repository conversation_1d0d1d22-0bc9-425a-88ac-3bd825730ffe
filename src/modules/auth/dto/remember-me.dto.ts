import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO cho response của API lấy thông tin rememberMe
 */
export class RememberMeResponseDto {
  @ApiProperty({
    description: 'Token truy cập',
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
  })
  accessToken: string;

  @ApiProperty({
    description: 'Thời gian hết hạn của token (giây)',
    example: 86400,
    required: false,
  })
  expiresIn?: number;

  @ApiProperty({
    description: 'Thời điểm hết hạn của token (timestamp)',
    example: 1746968772000,
  })
  expiresAt: number;

  @ApiProperty({
    description: 'Thông tin người dùng',
    example: {
      id: 1,
      email: '<EMAIL>',
      fullName: 'Nguyễn <PERSON>ăn <PERSON>',
    },
  })
  user: {
    id: number;
    email: string;
    fullName: string;
  };
}
