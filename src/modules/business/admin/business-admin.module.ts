import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import {
  CustomerProduct,
  UserClassification,
  CustomField,
  UserOrder,
  Inventory,
  UserConvert,
  UserConvertCustomer,
  Warehouse,
  PhysicalWarehouse,
  VirtualWarehouse,
  File,
  Folder,
  // New entities for complete product info
  PhysicalProduct,
  PhysicalProductVariant,
  DigitalProduct,
  DigitalProductVersion,
  ServiceProduct,
  ServicePackageOption,
  EventProduct,
  EventProductTicket,
  ComboProduct,
  EntityHasMedia,
} from '../entities';
import { Media } from '@modules/data/media/entities/media.entity';
import {
  ProductAdminController,
  CustomFieldAdminController,
  UserConvertAdminController,
  UserConvertCustomerAdminController,
  UserOrderAdminController,
  AdminWarehouseController,
  AdminPhysicalWarehouseController,
  AdminFileController,
  AdminFolderController
} from './controllers';

import {
  ProductAdminService,
  CustomFieldAdminService,
  UserConvertAdminService,
  UserConvertCustomerAdminService,
  UserOrderAdminService,
  AdminWarehouseService,
  AdminWarehouseCustomFieldService,
  AdminVirtualWarehouseService,
  AdminPhysicalWarehouseService,
  AdminFileService,
  AdminFolderService
} from './services';

import { ValidationHelper, WarehouseValidationHelper, FileValidationHelper, FolderValidationHelper, FileHelper, FolderHelper } from './helpers';
import {
  CustomerProductAdminRepository,
  CustomFieldRepository,
  UserClassificationRepository,
  UserConvertRepository,
  UserConvertCustomerRepository,
  UserOrderRepository,
  WarehouseRepository,
  PhysicalWarehouseRepository,
  VirtualWarehouseRepository,
  FileRepository,
  FolderRepository,
  // New repositories for complete product info
  PhysicalProductRepository,
  PhysicalProductVariantRepository,
  DigitalProductRepository,
  DigitalProductVersionRepository,
  ServiceProductRepository,
  ServicePackageOptionRepository,
  EventProductRepository,
  EventProductTicketRepository,
  ComboProductRepository,
  EntityHasMediaRepository,
} from '../repositories';
import { MediaRepository } from '@modules/data/media/repositories/media.repository';

/**
 * Module quản lý chức năng business cho admin
 */
@Module({
  imports: [
    TypeOrmModule.forFeature([
      CustomerProduct,
      UserClassification,
      CustomField,
      UserOrder,
      Inventory,
      UserConvert,
      UserConvertCustomer,
      Warehouse,
      PhysicalWarehouse,
      VirtualWarehouse,
      File,
      Folder,
      // New entities for complete product info
      PhysicalProduct,
      PhysicalProductVariant,
      DigitalProduct,
      DigitalProductVersion,
      ServiceProduct,
      ServicePackageOption,
      EventProduct,
      EventProductTicket,
      ComboProduct,
      EntityHasMedia,
      Media
    ])
  ],
  controllers: [
    ProductAdminController,
    CustomFieldAdminController,
    UserConvertAdminController,
    UserConvertCustomerAdminController,
    UserOrderAdminController,
    AdminWarehouseController,
    AdminPhysicalWarehouseController,
    AdminFileController,
    AdminFolderController
  ],
  providers: [
    // Helpers
    ValidationHelper,
    WarehouseValidationHelper,
    FileValidationHelper,
    FolderValidationHelper,
    FileHelper,
    FolderHelper,

    // Services
    ProductAdminService,
    CustomFieldAdminService,
    UserConvertAdminService,
    UserConvertCustomerAdminService,
    UserOrderAdminService,
    AdminWarehouseService,
    AdminWarehouseCustomFieldService,
    AdminVirtualWarehouseService,
    AdminPhysicalWarehouseService,
    AdminFileService,
    AdminFolderService,

    // Repositories
    CustomerProductAdminRepository,
    CustomFieldRepository,
    UserClassificationRepository,
    UserConvertRepository,
    UserConvertCustomerRepository,
    UserOrderRepository,
    WarehouseRepository,
    PhysicalWarehouseRepository,
    VirtualWarehouseRepository,
    FileRepository,
    FolderRepository,
    // New repositories for complete product info
    PhysicalProductRepository,
    PhysicalProductVariantRepository,
    DigitalProductRepository,
    DigitalProductVersionRepository,
    ServiceProductRepository,
    ServicePackageOptionRepository,
    EventProductRepository,
    EventProductTicketRepository,
    ComboProductRepository,
    EntityHasMediaRepository,
    MediaRepository,

    // Helpers
    ValidationHelper
  ],
  exports: [
    TypeOrmModule,
    ProductAdminService,
    CustomFieldAdminService,
    UserConvertAdminService,
    UserConvertCustomerAdminService,
    UserOrderAdminService,
    AdminWarehouseService,
    AdminWarehouseCustomFieldService,
    AdminVirtualWarehouseService,
    AdminPhysicalWarehouseService,
    AdminFileService,
    AdminFolderService
  ],
})
export class BusinessAdminModule {}
