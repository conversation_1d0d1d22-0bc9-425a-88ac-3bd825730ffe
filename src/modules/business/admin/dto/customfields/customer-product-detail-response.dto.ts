import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsOptional, IsObject } from 'class-validator';
import { CustomerProductResponseDto } from './customer-product-response.dto';

/**
 * DTO cho response trả về chi tiết hoàn chỉnh sản phẩm khách hàng
 * Mở rộng từ CustomerProductResponseDto và thêm thông tin từ tất cả bảng liên quan
 */
export class CustomerProductDetailResponseDto extends CustomerProductResponseDto {

  // Thông tin từ physical_products
  @ApiProperty({
    description: 'Thông tin sản phẩm vật lý',
    required: false,
    nullable: true,
  })
  @IsOptional()
  @IsObject()
  physicalInfo?: any;

  @ApiProperty({
    description: 'Danh sách variants cho sản phẩm vật lý',
    required: false,
    nullable: true,
  })
  @IsOptional()
  @IsArray()
  variants?: any[];

  // Thông tin từ digital_products
  @ApiProperty({
    description: 'Thông tin sản phẩm số',
    required: false,
    nullable: true,
  })
  @IsOptional()
  @IsObject()
  digitalInfo?: any;

  @ApiProperty({
    description: 'Danh sách versions cho sản phẩm số',
    required: false,
    nullable: true,
  })
  @IsOptional()
  @IsArray()
  versions?: any[];

  // Thông tin từ service_products
  @ApiProperty({
    description: 'Thông tin sản phẩm dịch vụ',
    required: false,
    nullable: true,
  })
  @IsOptional()
  @IsObject()
  serviceInfo?: any;

  @ApiProperty({
    description: 'Danh sách packages cho sản phẩm dịch vụ',
    required: false,
    nullable: true,
  })
  @IsOptional()
  @IsArray()
  packages?: any[];

  // Thông tin từ event_products
  @ApiProperty({
    description: 'Thông tin sản phẩm sự kiện',
    required: false,
    nullable: true,
  })
  @IsOptional()
  @IsObject()
  eventInfo?: any;

  @ApiProperty({
    description: 'Danh sách tickets cho sản phẩm sự kiện',
    required: false,
    nullable: true,
  })
  @IsOptional()
  @IsArray()
  tickets?: any[];

  // Thông tin từ combo_products
  @ApiProperty({
    description: 'Thông tin sản phẩm combo',
    required: false,
    nullable: true,
  })
  @IsOptional()
  @IsObject()
  comboInfo?: any;

  @ApiProperty({
    description: 'Hình ảnh combo (product_combo_id)',
    required: false,
    nullable: true,
  })
  @IsOptional()
  @IsArray()
  comboImages?: any[];

  // Hình ảnh chung
  @ApiProperty({
    description: 'Danh sách hình ảnh sản phẩm từ entity_has_media và media_data',
    required: false,
    nullable: true,
  })
  @IsOptional()
  @IsArray()
  images?: any[];
}
