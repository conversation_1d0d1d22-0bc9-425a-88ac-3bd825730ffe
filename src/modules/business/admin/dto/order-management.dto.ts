import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty, IsOptional, IsString, IsNumber, IsArray, ValidateNested, IsBoolean, MaxLength, Min } from 'class-validator';
import { Type } from 'class-transformer';
import { OrderStatusEnum, ShippingStatusEnum } from '../../enums';

/**
 * DTO cho việc cập nhật trạng thái đơn hàng
 */
export class UpdateOrderStatusDto {
  @ApiProperty({
    description: 'ID của đơn hàng',
    example: 1,
  })
  @IsNotEmpty({ message: 'ID đơn hàng không được để trống' })
  @IsNumber({}, { message: 'ID đơn hàng phải là số' })
  @Min(1, { message: 'ID đơn hàng phải lớn hơn 0' })
  orderId: number;

  @ApiProperty({
    description: 'Trạng thái đơn hàng mới',
    enum: OrderStatusEnum,
    example: OrderStatusEnum.CONFIRMED,
  })
  @IsNotEmpty({ message: 'Trạng thái đơn hàng không được để trống' })
  @IsEnum(OrderStatusEnum, { message: 'Trạng thái đơn hàng không hợp lệ' })
  orderStatus: OrderStatusEnum;

  @ApiProperty({
    description: 'Lý do thay đổi trạng thái (tùy chọn)',
    example: 'Xác nhận đơn hàng sau khi kiểm tra thông tin',
    required: false,
    maxLength: 500,
  })
  @IsOptional()
  @IsString({ message: 'Lý do phải là chuỗi' })
  @MaxLength(500, { message: 'Lý do không được vượt quá 500 ký tự' })
  reason?: string;
}

/**
 * DTO cho việc cập nhật trạng thái vận chuyển
 */
export class UpdateShippingStatusDto {
  @ApiProperty({
    description: 'ID của đơn hàng',
    example: 1,
  })
  @IsNotEmpty({ message: 'ID đơn hàng không được để trống' })
  @IsNumber({}, { message: 'ID đơn hàng phải là số' })
  @Min(1, { message: 'ID đơn hàng phải lớn hơn 0' })
  orderId: number;

  @ApiProperty({
    description: 'Trạng thái vận chuyển mới',
    enum: ShippingStatusEnum,
    example: ShippingStatusEnum.SHIPPED,
  })
  @IsNotEmpty({ message: 'Trạng thái vận chuyển không được để trống' })
  @IsEnum(ShippingStatusEnum, { message: 'Trạng thái vận chuyển không hợp lệ' })
  shippingStatus: ShippingStatusEnum;

  @ApiProperty({
    description: 'Thông tin vận chuyển bổ sung (tùy chọn)',
    example: 'Đã giao cho đơn vị vận chuyển GHN',
    required: false,
    maxLength: 500,
  })
  @IsOptional()
  @IsString({ message: 'Thông tin vận chuyển phải là chuỗi' })
  @MaxLength(500, { message: 'Thông tin vận chuyển không được vượt quá 500 ký tự' })
  note?: string;
}

/**
 * DTO cho việc hủy đơn hàng
 */
export class AdminCancelOrderDto {
  @ApiProperty({
    description: 'ID của đơn hàng',
    example: 1,
  })
  @IsNotEmpty({ message: 'ID đơn hàng không được để trống' })
  @IsNumber({}, { message: 'ID đơn hàng phải là số' })
  @Min(1, { message: 'ID đơn hàng phải lớn hơn 0' })
  orderId: number;

  @ApiProperty({
    description: 'Lý do hủy đơn hàng',
    example: 'Khách hàng yêu cầu hủy do thay đổi ý định',
    maxLength: 500,
  })
  @IsNotEmpty({ message: 'Lý do hủy không được để trống' })
  @IsString({ message: 'Lý do hủy phải là chuỗi' })
  @MaxLength(500, { message: 'Lý do hủy không được vượt quá 500 ký tự' })
  cancelReason: string;

  @ApiProperty({
    description: 'Có hoàn tiền không',
    example: true,
  })
  @IsNotEmpty({ message: 'Thông tin hoàn tiền không được để trống' })
  @IsBoolean({ message: 'Thông tin hoàn tiền phải là boolean' })
  refund: boolean;
}

/**
 * DTO cho việc xác nhận đơn hàng
 */
export class AdminConfirmOrderDto {
  @ApiProperty({
    description: 'ID của đơn hàng',
    example: 1,
  })
  @IsNotEmpty({ message: 'ID đơn hàng không được để trống' })
  @IsNumber({}, { message: 'ID đơn hàng phải là số' })
  @Min(1, { message: 'ID đơn hàng phải lớn hơn 0' })
  orderId: number;

  @ApiProperty({
    description: 'Ghi chú xác nhận (tùy chọn)',
    example: 'Đã kiểm tra thông tin và xác nhận đơn hàng',
    required: false,
    maxLength: 500,
  })
  @IsOptional()
  @IsString({ message: 'Ghi chú phải là chuỗi' })
  @MaxLength(500, { message: 'Ghi chú không được vượt quá 500 ký tự' })
  note?: string;
}

/**
 * DTO cho việc cập nhật trạng thái thanh toán
 */
export class UpdatePaymentStatusDto {
  @ApiProperty({
    description: 'ID của đơn hàng',
    example: 1,
  })
  @IsNotEmpty({ message: 'ID đơn hàng không được để trống' })
  @IsNumber({}, { message: 'ID đơn hàng phải là số' })
  @Min(1, { message: 'ID đơn hàng phải lớn hơn 0' })
  orderId: number;

  @ApiProperty({
    description: 'Trạng thái thanh toán',
    enum: ['pending', 'paid', 'failed', 'refunded', 'partial_refund'],
    example: 'paid',
  })
  @IsNotEmpty({ message: 'Trạng thái thanh toán không được để trống' })
  @IsEnum(['pending', 'paid', 'failed', 'refunded', 'partial_refund'], { 
    message: 'Trạng thái thanh toán không hợp lệ' 
  })
  paymentStatus: 'pending' | 'paid' | 'failed' | 'refunded' | 'partial_refund';

  @ApiProperty({
    description: 'Ghi chú thanh toán (tùy chọn)',
    example: 'Đã nhận thanh toán qua chuyển khoản',
    required: false,
    maxLength: 500,
  })
  @IsOptional()
  @IsString({ message: 'Ghi chú thanh toán phải là chuỗi' })
  @MaxLength(500, { message: 'Ghi chú thanh toán không được vượt quá 500 ký tự' })
  paymentNote?: string;
}

/**
 * DTO cho việc cập nhật hàng loạt đơn hàng
 */
export class BulkUpdateOrderStatusDto {
  @ApiProperty({
    description: 'Danh sách ID đơn hàng',
    example: [1, 2, 3],
    type: [Number],
  })
  @IsNotEmpty({ message: 'Danh sách ID đơn hàng không được để trống' })
  @IsArray({ message: 'Danh sách ID đơn hàng phải là mảng' })
  @IsNumber({}, { each: true, message: 'Mỗi ID đơn hàng phải là số' })
  @Min(1, { each: true, message: 'Mỗi ID đơn hàng phải lớn hơn 0' })
  orderIds: number[];

  @ApiProperty({
    description: 'Trạng thái đơn hàng mới',
    enum: OrderStatusEnum,
    example: OrderStatusEnum.CONFIRMED,
  })
  @IsNotEmpty({ message: 'Trạng thái đơn hàng không được để trống' })
  @IsEnum(OrderStatusEnum, { message: 'Trạng thái đơn hàng không hợp lệ' })
  orderStatus: OrderStatusEnum;

  @ApiProperty({
    description: 'Lý do thay đổi trạng thái (tùy chọn)',
    example: 'Xác nhận hàng loạt đơn hàng',
    required: false,
    maxLength: 500,
  })
  @IsOptional()
  @IsString({ message: 'Lý do phải là chuỗi' })
  @MaxLength(500, { message: 'Lý do không được vượt quá 500 ký tự' })
  reason?: string;
}

/**
 * DTO response cho việc cập nhật trạng thái
 */
export class OrderStatusUpdateResponseDto {
  @ApiProperty({
    description: 'ID của đơn hàng',
    example: 1,
  })
  orderId: number;

  @ApiProperty({
    description: 'Trạng thái cũ',
    example: 'pending',
  })
  oldStatus: string;

  @ApiProperty({
    description: 'Trạng thái mới',
    example: 'confirmed',
  })
  newStatus: string;

  @ApiProperty({
    description: 'Thời gian cập nhật',
    example: 1704067200000,
  })
  updatedAt: number;

  @ApiProperty({
    description: 'ID nhân viên thực hiện',
    example: 1,
  })
  updatedBy: number;
}

/**
 * DTO response cho việc cập nhật hàng loạt
 */
export class BulkOrderUpdateResponseDto {
  @ApiProperty({
    description: 'Tổng số đơn hàng được yêu cầu cập nhật',
    example: 10,
  })
  totalRequested: number;

  @ApiProperty({
    description: 'Số đơn hàng cập nhật thành công',
    example: 8,
  })
  successCount: number;

  @ApiProperty({
    description: 'Số đơn hàng cập nhật thất bại',
    example: 2,
  })
  failureCount: number;

  @ApiProperty({
    description: 'Chi tiết kết quả từng đơn hàng',
    type: [OrderStatusUpdateResponseDto],
  })
  results: OrderStatusUpdateResponseDto[];

  @ApiProperty({
    description: 'Thông báo tổng kết',
    example: 'Cập nhật thành công 8/10 đơn hàng',
  })
  message: string;
}
