import { Injectable } from '@nestjs/common';
import { AppException } from '@common/exceptions';
import { BUSINESS_ADMIN_ERROR_CODES } from '@modules/business/admin/exceptions';
import { CustomField, UserConvert, UserConvertCustomer, CustomerProduct } from '@modules/business/entities';
import { EntityStatusEnum } from '@modules/business/enums/entity-status.enum';
import { PriceTypeEnum } from '@modules/business/enums/price-type.enum';

/**
 * Helper xử lý validation cho module Business Admin
 */
@Injectable()
export class ValidationHelper {
  /**
   * Kiểm tra sản phẩm tồn tại
   * @param product Sản phẩm cần kiểm tra
   * @throws AppException nếu sản phẩm không tồn tại
   */
  validateProductExists(product: CustomerProduct | null): asserts product is CustomerProduct {
    if (!product) {
      throw new AppException(
        BUSINESS_ADMIN_ERROR_CODES.USER_PRODUCT_NOT_FOUND,
        'Sản phẩm không tồn tại',
      );
    }
  }

  /**
   * Kiểm tra cấu trúc giá của sản phẩm dựa trên loại giá
   * @param product Sản phẩm cần kiểm tra
   * @throws AppException nếu cấu trúc giá không hợp lệ
   */
  validateProductPrice(product: CustomerProduct | null): void {
    // Đảm bảo sản phẩm tồn tại
    this.validateProductExists(product);

    // Kiểm tra cấu trúc giá dựa trên loại giá
    // Sau khi gọi validateProductExists, TypeScript biết rằng product không phải là null
    this.validateProductPriceStructure(product.price, product.typePrice);
  }

  /**
   * Kiểm tra trường tùy chỉnh tồn tại
   * @param customField Trường tùy chỉnh cần kiểm tra
   * @throws AppException nếu trường tùy chỉnh không tồn tại
   */
  validateCustomFieldExists(customField: CustomField | null): void {
    if (!customField) {
      throw new AppException(
        BUSINESS_ADMIN_ERROR_CODES.CUSTOM_FIELD_NOT_FOUND,
        'Trường tùy chỉnh không tồn tại',
      );
    }
  }

  /**
   * Kiểm tra configId không trùng lặp
   * @param configId ConfigId cần kiểm tra
   * @param existingConfigId ConfigId đã tồn tại
   * @throws AppException nếu configId trùng lặp
   */
  validateConfigIdNotDuplicate(configId: string, existingConfigId: string | null): void {
    if (existingConfigId && existingConfigId !== configId) {
      throw new AppException(
        BUSINESS_ADMIN_ERROR_CODES.CONFIG_ID_DUPLICATE,
        'ConfigId đã tồn tại',
      );
    }
  }

  /**
   * Kiểm tra dữ liệu trường tùy chỉnh hợp lệ
   * @param configId ID cấu hình
   * @param label Nhãn hiển thị
   * @param type Loại trường
   * @throws AppException nếu dữ liệu không hợp lệ
   */
  validateCustomFieldData(configId: string, label: string, type: string): void {
    if (!configId || !label || !type) {
      throw new AppException(
        BUSINESS_ADMIN_ERROR_CODES.CUSTOM_FIELD_VALIDATION_ERROR,
        'Thiếu thông tin bắt buộc cho trường tùy chỉnh',
      );
    }

    // Kiểm tra định dạng configId (chỉ cho phép chữ cái, số và dấu gạch dưới)
    if (!/^[a-zA-Z0-9_]+$/.test(configId)) {
      throw new AppException(
        BUSINESS_ADMIN_ERROR_CODES.CUSTOM_FIELD_VALIDATION_ERROR,
        'ConfigId chỉ được chứa chữ cái, số và dấu gạch dưới',
      );
    }
  }

  /**
   * Kiểm tra chuyển đổi trạng thái sản phẩm hợp lệ
   * @param currentStatus Trạng thái hiện tại
   * @param newStatus Trạng thái mới
   * @throws AppException nếu chuyển đổi trạng thái không hợp lệ
   */
  validateStatusTransition(currentStatus: EntityStatusEnum, newStatus: EntityStatusEnum): void {
    // Trường hợp trạng thái không thay đổi
    if (currentStatus === newStatus) {
      return;
    }

    // Kiểm tra các chuyển đổi trạng thái hợp lệ
    const validTransitions: Record<EntityStatusEnum, EntityStatusEnum[]> = {
      [EntityStatusEnum.PENDING]: [EntityStatusEnum.APPROVED, EntityStatusEnum.REJECTED],
      [EntityStatusEnum.APPROVED]: [],
      [EntityStatusEnum.REJECTED]: [],
      [EntityStatusEnum.DELETED]: [],
    };

    if (!validTransitions[currentStatus]?.includes(newStatus)) {
      throw new AppException(
        BUSINESS_ADMIN_ERROR_CODES.PRODUCT_STATUS_INVALID_TRANSITION,
        `Không thể chuyển trạng thái từ ${currentStatus} sang ${newStatus}`,
      );
    }
  }

  /**
   * Kiểm tra lý do từ chối khi chuyển trạng thái sang REJECTED
   * @param newStatus Trạng thái mới
   * @param rejectReason Lý do từ chối
   * @throws AppException nếu thiếu lý do từ chối
   */
  validateRejectReason(newStatus: EntityStatusEnum, rejectReason?: string): void {
    if (newStatus === EntityStatusEnum.REJECTED && (!rejectReason || rejectReason.trim() === '')) {
      throw new AppException(
        BUSINESS_ADMIN_ERROR_CODES.PRODUCT_REJECT_REASON_REQUIRED,
        'Cần cung cấp lý do từ chối khi chuyển trạng thái sang REJECTED',
      );
    }
  }

  /**
   * Kiểm tra cấu trúc giá sản phẩm dựa trên loại giá
   * @param price Giá sản phẩm
   * @param typePrice Loại giá
   * @throws AppException nếu cấu trúc giá không hợp lệ
   */
  validateProductPriceStructure(price: any, typePrice: PriceTypeEnum | null): void {
    // Xử lý trường hợp typePrice là null (dữ liệu cũ từ migration)
    if (typePrice === null || typePrice === undefined) {
      throw new AppException(
        BUSINESS_ADMIN_ERROR_CODES.PRODUCT_PRICE_VALIDATION_ERROR,
        'Sản phẩm chưa có thông tin loại giá. Vui lòng cập nhật thông tin giá cho sản phẩm trước khi phê duyệt.'
      );
    }

    switch (typePrice) {
      case PriceTypeEnum.HAS_PRICE:
        if (!price || typeof price !== 'object') {
          throw new AppException(
            BUSINESS_ADMIN_ERROR_CODES.PRODUCT_PRICE_VALIDATION_ERROR,
            `Giá sản phẩm phải là một đối tượng khi loại giá là HAS_PRICE. Hiện tại: ${typeof price === 'object' ? JSON.stringify(price) : price} (type: ${typeof price})`
          );
        }

        if (!price.currency || !price.listPrice || !price.salePrice) {
          const missingFields: string[] = [];
          if (!price.currency) missingFields.push('currency');
          if (!price.listPrice) missingFields.push('listPrice');
          if (!price.salePrice) missingFields.push('salePrice');

          throw new AppException(
            BUSINESS_ADMIN_ERROR_CODES.PRODUCT_PRICE_VALIDATION_ERROR,
            `Giá sản phẩm phải có đủ currency, listPrice và salePrice khi loại giá là HAS_PRICE. Thiếu: ${missingFields.join(', ')}. Hiện tại: ${JSON.stringify(price)}`
          );
        }

        if (price.salePrice > price.listPrice) {
          throw new AppException(
            BUSINESS_ADMIN_ERROR_CODES.PRODUCT_PRICE_VALIDATION_ERROR,
            'Giá bán (salePrice) phải nhỏ hơn hoặc bằng giá niêm yết (listPrice)'
          );
        }
        break;

      case PriceTypeEnum.STRING_PRICE:
        if (!price || typeof price !== 'object' || !price.priceDescription) {
          throw new AppException(
            BUSINESS_ADMIN_ERROR_CODES.PRODUCT_PRICE_VALIDATION_ERROR,
            'Giá sản phẩm phải có trường priceDescription khi loại giá là STRING_PRICE'
          );
        }
        break;

      case PriceTypeEnum.NO_PRICE:
        if (price !== null) {
          throw new AppException(
            BUSINESS_ADMIN_ERROR_CODES.PRODUCT_PRICE_VALIDATION_ERROR,
            'Giá sản phẩm phải là null khi loại giá là NO_PRICE'
          );
        }
        break;

      default:
        throw new AppException(
          BUSINESS_ADMIN_ERROR_CODES.PRODUCT_PRICE_VALIDATION_ERROR,
          `Loại giá không hợp lệ: ${typePrice}`
        );
    }
  }

  /**
   * Kiểm tra bản ghi chuyển đổi khách hàng có tồn tại không
   * @param convert Bản ghi chuyển đổi khách hàng cần kiểm tra
   * @throws AppException nếu bản ghi không tồn tại
   */
  validateUserConvertExists(convert: UserConvert | null): void {
    if (!convert) {
      throw new AppException(
        BUSINESS_ADMIN_ERROR_CODES.USER_CONVERT_NOT_FOUND,
        'Không tìm thấy bản ghi chuyển đổi khách hàng',
      );
    }
  }

  /**
   * Kiểm tra khách hàng chuyển đổi có tồn tại không
   * @param customer Khách hàng chuyển đổi cần kiểm tra
   * @throws AppException nếu khách hàng không tồn tại
   */
  validateUserConvertCustomerExists(customer: UserConvertCustomer | null): void {
    if (!customer) {
      throw new AppException(
        BUSINESS_ADMIN_ERROR_CODES.USER_CONVERT_CUSTOMER_NOT_FOUND,
        'Không tìm thấy khách hàng chuyển đổi',
      );
    }
  }

  /**
   * Kiểm tra đơn hàng có tồn tại không
   * @param order Đơn hàng cần kiểm tra
   * @throws AppException nếu đơn hàng không tồn tại
   */
  validateUserOrderExists(order: Record<string, any> | null): void {
    if (!order) {
      throw new AppException(
        BUSINESS_ADMIN_ERROR_CODES.USER_ORDER_NOT_FOUND,
        'Không tìm thấy đơn hàng',
      );
    }
  }
}
