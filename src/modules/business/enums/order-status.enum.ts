 /**
 * Enum định nghĩa các trạng thái đơn hàng
 * Tương ứng với order_status_enum trong database
 */
export enum OrderStatusEnum {
  /**
   * Đang chờ xử lý - Đơn hàng vừa được tạo
   */
  PENDING = 'pending',

  /**
   * Đ<PERSON> xác nhận - Đơn hàng đã được xác nhận
   */
  CONFIRMED = 'confirmed',

  /**
   * Đang xử lý - Đơn hàng đang được chuẩn bị
   */
  PROCESSING = 'processing',

  /**
   * Đã hoàn thành - Đơn hàng đã được giao thành công
   */
  COMPLETED = 'completed',

  /**
   * Đã hủy - Đơn hàng bị hủy
   */
  CANCELLED = 'cancelled'
}

/**
 * Mô tả trạng thái đơn hàng bằng tiếng Việt
 */
export const ORDER_STATUS_DESCRIPTIONS = {
  [OrderStatusEnum.PENDING]: 'Đang chờ xử lý',
  [OrderStatusEnum.CONFIRMED]: 'Đã xác nhận',
  [OrderStatusEnum.PROCESSING]: 'Đang xử lý',
  [OrderStatusEnum.COMPLETED]: 'Đã hoàn thành',
  [OrderStatusEnum.CANCELLED]: 'Đã hủy'
};
