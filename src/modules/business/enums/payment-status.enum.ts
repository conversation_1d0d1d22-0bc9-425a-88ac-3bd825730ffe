/**
 * Enum định nghĩa các trạng thái thanh toán
 */
export enum PaymentStatusEnum {
  /**
   * Chờ thanh toán
   */
  PENDING = 'PENDING',

  /**
   * Đ<PERSON> thanh toán
   */
  PAID = 'PAID',

  /**
   * <PERSON><PERSON> toán thất bại
   */
  FAILED = 'FAILED',

  /**
   * Đ<PERSON> hoàn tiền
   */
  REFUNDED = 'REFUNDED',

  /**
   * Thanh toán một phần
   */
  PARTIAL_PAID = 'PARTIAL_PAID',

  /**
   * Đã hủy
   */
  CANCELLED = 'CANCELLED'
}

/**
 * <PERSON><PERSON> tả trạng thái thanh toán bằng tiếng Việt
 */
export const PAYMENT_STATUS_DESCRIPTIONS = {
  [PaymentStatusEnum.PENDING]: 'Chờ thanh toán',
  [PaymentStatusEnum.PAID]: 'Đã thanh toán',
  [PaymentStatusEnum.FAILED]: 'Thanh toán thất bại',
  [PaymentStatusEnum.REFUNDED]: 'Đã hoàn tiền',
  [PaymentStatusEnum.PARTIAL_PAID]: 'Thanh toán một phần',
  [PaymentStatusEnum.CANCELLED]: 'Đã hủy'
};
