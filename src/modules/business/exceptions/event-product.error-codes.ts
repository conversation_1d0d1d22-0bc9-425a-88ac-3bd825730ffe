import { ErrorCode } from '@common/exceptions/app.exception';
import { HttpStatus } from '@nestjs/common';
import { CUSTOMER_PRODUCT_ERROR_CODES } from './customer-product.error-codes';

/**
 * Error codes cho Event Product module
 * Range: 4200-4299 (extends customer product range 4000-4099)
 * 
 * Thừa kế tất cả error codes từ CUSTOMER_PRODUCT_ERROR_CODES
 * và thêm các error codes đặc biệt cho event product
 */
export const EVENT_PRODUCT_ERROR_CODES = {
  // ========== THỪA KẾ TỪ CUSTOMER PRODUCT ==========
  ...CUSTOMER_PRODUCT_ERROR_CODES,

  // ========== EVENT PRODUCT SPECIFIC ERRORS (4200-4219) ==========
  EVENT_NOT_FOUND: new ErrorCode(4200, 'Không tìm thấy sản phẩm sự kiện', HttpStatus.NOT_FOUND),
  EVENT_CREATION_FAILED: new ErrorCode(4201, 'Lỗi khi tạo sản phẩm sự kiện', HttpStatus.BAD_REQUEST),
  EVENT_UPDATE_FAILED: new ErrorCode(4202, 'Lỗi khi cập nhật sản phẩm sự kiện', HttpStatus.BAD_REQUEST),
  EVENT_DELETE_FAILED: new ErrorCode(4203, 'Lỗi khi xóa sản phẩm sự kiện', HttpStatus.BAD_REQUEST),
  EVENT_FIND_FAILED: new ErrorCode(4204, 'Lỗi khi tìm kiếm sản phẩm sự kiện', HttpStatus.BAD_REQUEST),
  INVALID_PRODUCT_TYPE: new ErrorCode(4205, 'Loại sản phẩm không hợp lệ cho Event Product API', HttpStatus.BAD_REQUEST),

  // ========== EVENT DATE/TIME VALIDATION (4220-4229) ==========
  INVALID_EVENT_DATES: new ErrorCode(4220, 'Thời gian sự kiện không hợp lệ', HttpStatus.BAD_REQUEST),
  START_DATE_AFTER_END_DATE: new ErrorCode(4221, 'Thời gian bắt đầu phải trước thời gian kết thúc', HttpStatus.BAD_REQUEST),
  EVENT_IN_PAST: new ErrorCode(4222, 'Không thể tạo sự kiện trong quá khứ', HttpStatus.BAD_REQUEST),
  INVALID_TIMEZONE: new ErrorCode(4223, 'Múi giờ không hợp lệ', HttpStatus.BAD_REQUEST),
  EVENT_DURATION_TOO_SHORT: new ErrorCode(4224, 'Thời gian sự kiện quá ngắn', HttpStatus.BAD_REQUEST),
  EVENT_DURATION_TOO_LONG: new ErrorCode(4225, 'Thời gian sự kiện quá dài', HttpStatus.BAD_REQUEST),

  // ========== PARTICIPATION TYPE VALIDATION (4230-4239) ==========
  INVALID_PARTICIPATION_TYPE: new ErrorCode(4230, 'Hình thức tham gia không hợp lệ', HttpStatus.BAD_REQUEST),
  LOCATION_REQUIRED_FOR_OFFLINE: new ErrorCode(4231, 'Địa điểm bắt buộc cho sự kiện offline', HttpStatus.BAD_REQUEST),
  URL_REQUIRED_FOR_ONLINE: new ErrorCode(4232, 'Đường dẫn bắt buộc cho sự kiện online', HttpStatus.BAD_REQUEST),
  INVALID_PARTICIPATION_URL: new ErrorCode(4233, 'Đường dẫn tham gia không hợp lệ', HttpStatus.BAD_REQUEST),
  LOCATION_AND_URL_REQUIRED_FOR_HYBRID: new ErrorCode(4234, 'Địa điểm và đường dẫn bắt buộc cho sự kiện hybrid', HttpStatus.BAD_REQUEST),

  // ========== EVENT TICKET ERRORS (4240-4259) ==========
  TICKET_NOT_FOUND: new ErrorCode(4240, 'Không tìm thấy vé sự kiện', HttpStatus.NOT_FOUND),
  TICKET_CREATION_FAILED: new ErrorCode(4241, 'Lỗi khi tạo vé sự kiện', HttpStatus.BAD_REQUEST),
  TICKET_UPDATE_FAILED: new ErrorCode(4242, 'Lỗi khi cập nhật vé sự kiện', HttpStatus.BAD_REQUEST),
  TICKET_DELETE_FAILED: new ErrorCode(4243, 'Lỗi khi xóa vé sự kiện', HttpStatus.BAD_REQUEST),
  TICKET_FIND_FAILED: new ErrorCode(4244, 'Lỗi khi tìm kiếm vé sự kiện', HttpStatus.BAD_REQUEST),

  // ========== TICKET VALIDATION (4250-4259) ==========
  INVALID_TICKET_PRICE: new ErrorCode(4250, 'Giá vé không hợp lệ', HttpStatus.BAD_REQUEST),
  INVALID_TICKET_QUANTITY: new ErrorCode(4251, 'Số lượng vé không hợp lệ', HttpStatus.BAD_REQUEST),
  TICKET_SALE_DATES_INVALID: new ErrorCode(4252, 'Thời gian bán vé không hợp lệ', HttpStatus.BAD_REQUEST),
  TICKET_SALE_AFTER_EVENT: new ErrorCode(4253, 'Thời gian bán vé phải trước khi sự kiện bắt đầu', HttpStatus.BAD_REQUEST),
  TICKET_MIN_MAX_QUANTITY_INVALID: new ErrorCode(4254, 'Số lượng tối thiểu/tối đa mua vé không hợp lệ', HttpStatus.BAD_REQUEST),
  DUPLICATE_TICKET_SKU: new ErrorCode(4255, 'Mã SKU vé đã tồn tại', HttpStatus.CONFLICT),
  TICKET_QUANTITY_EXCEEDED: new ErrorCode(4256, 'Số lượng vé vượt quá giới hạn', HttpStatus.BAD_REQUEST),

  // ========== TICKET OPERATIONS (4260-4269) ==========
  INVALID_TICKET_OPERATION: new ErrorCode(4260, 'Thao tác với vé không hợp lệ', HttpStatus.BAD_REQUEST),
  TICKET_OPERATION_NOT_ALLOWED: new ErrorCode(4261, 'Không được phép thực hiện thao tác này với vé', HttpStatus.FORBIDDEN),
  TICKET_ALREADY_SOLD: new ErrorCode(4262, 'Không thể xóa vé đã được bán', HttpStatus.BAD_REQUEST),
  TICKET_SALE_STARTED: new ErrorCode(4263, 'Không thể chỉnh sửa vé đã bắt đầu bán', HttpStatus.BAD_REQUEST),

  // ========== EVENT STATUS VALIDATION (4270-4279) ==========
  EVENT_ALREADY_STARTED: new ErrorCode(4270, 'Sự kiện đã bắt đầu, không thể chỉnh sửa', HttpStatus.BAD_REQUEST),
  EVENT_ALREADY_ENDED: new ErrorCode(4271, 'Sự kiện đã kết thúc', HttpStatus.BAD_REQUEST),
  EVENT_CANCELLED: new ErrorCode(4272, 'Sự kiện đã bị hủy', HttpStatus.BAD_REQUEST),
  CANNOT_CANCEL_STARTED_EVENT: new ErrorCode(4273, 'Không thể hủy sự kiện đã bắt đầu', HttpStatus.BAD_REQUEST),

  // ========== INTEGRATION ERRORS (4290-4299) ==========
  CALENDAR_INTEGRATION_FAILED: new ErrorCode(4290, 'Lỗi tích hợp lịch', HttpStatus.INTERNAL_SERVER_ERROR),
  NOTIFICATION_SEND_FAILED: new ErrorCode(4291, 'Lỗi gửi thông báo sự kiện', HttpStatus.INTERNAL_SERVER_ERROR),
  TICKET_GENERATION_FAILED: new ErrorCode(4292, 'Lỗi tạo vé điện tử', HttpStatus.INTERNAL_SERVER_ERROR),
  QR_CODE_GENERATION_FAILED: new ErrorCode(4293, 'Lỗi tạo mã QR', HttpStatus.INTERNAL_SERVER_ERROR),
  EMAIL_INVITATION_FAILED: new ErrorCode(4294, 'Lỗi gửi email mời tham gia', HttpStatus.INTERNAL_SERVER_ERROR),
} as const;

/**
 * Type helper để đảm bảo type safety khi sử dụng error codes
 */
export type EventProductErrorCode = typeof EVENT_PRODUCT_ERROR_CODES[keyof typeof EVENT_PRODUCT_ERROR_CODES];
