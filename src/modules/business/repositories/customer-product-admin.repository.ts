import { Injectable, Logger } from '@nestjs/common';
import { DataSource, Repository, SelectQueryBuilder, In, FindManyOptions, Like, FindOperator } from 'typeorm';
import { CustomerProduct } from '@modules/business/entities';
import { PaginatedResult } from '@common/response/api-response-dto';
import { QueryCustomerProductDto } from '@modules/business/admin/dto';
import { EntityStatusEnum } from '@modules/business/enums';

/**
 * Repository xử lý truy vấn dữ liệu cho entity CustomerProduct trong module admin
 */
@Injectable()
export class CustomerProductAdminRepository extends Repository<CustomerProduct> {
  protected readonly logger = new Logger(CustomerProductAdminRepository.name);

  constructor(private dataSource: DataSource) {
    super(CustomerProduct, dataSource.createEntityManager());
  }

  /**
   * Tạo query builder c<PERSON> bản cho CustomerProduct
   * @returns SelectQueryBuilder<CustomerProduct>
   */
  protected createBaseQuery(): SelectQueryBuilder<CustomerProduct> {
    return this.createQueryBuilder('customerProduct')
      .select([
        'customerProduct.id',
        'customerProduct.name',
        'customerProduct.price',
        'customerProduct.typePrice',
        'customerProduct.productType',
        'customerProduct.description',
        'customerProduct.tags',
        'customerProduct.userId',
        'customerProduct.createdAt',
        'customerProduct.updatedAt',
        'customerProduct.status',
        'customerProduct.customFields'
      ]);
  }

  /**
   * Tìm kiếm sản phẩm với các tham số lọc
   * @param queryParams Tham số lọc, có thể bao gồm userId để lọc theo người dùng cụ thể
   * @returns Danh sách sản phẩm phân trang
   */
  async findProducts(queryParams: QueryCustomerProductDto): Promise<PaginatedResult<CustomerProduct>> {
    this.logger.log(`Tìm kiếm sản phẩm với các tham số: ${JSON.stringify(queryParams)}`);

    const {
      page = 1,
      limit = 10,
      search,
      sortBy = 'createdAt', // Mặc định sắp xếp theo thời gian tạo
      sortDirection = 'DESC',
      userId,
      status,
    } = queryParams;

    const skip = (page - 1) * limit;

    // Tạo query builder cơ bản
    const query = this.createBaseQuery();
    this.logger.log(`Đã tạo query builder cơ bản`);

    // Thêm điều kiện tìm kiếm
    if (search) {
      this.logger.log(`Thêm điều kiện tìm kiếm với từ khóa: ${search}`);
      query.andWhere('(customerProduct.name ILIKE :search OR customerProduct.description ILIKE :search)', {
        search: `%${search}%`,
      });
    }

    // Lọc theo người dùng (sử dụng trường userId)
    if (userId) {
      this.logger.log(`Lọc theo người dùng: ${userId}`);
      query.andWhere('customerProduct.userId = :userId', { userId });
    }

    // Lọc theo trạng thái
    if (status) {
      this.logger.log(`Lọc theo trạng thái: ${status}`);
      query.andWhere('customerProduct.status = :status', { status });
    }

    // Sắp xếp
    if (sortBy && sortDirection) {
      this.logger.log(`Sắp xếp theo: ${sortBy} ${sortDirection}`);
      query.orderBy(`customerProduct.${sortBy}`, sortDirection as 'ASC' | 'DESC');
    }

    // Phân trang
    query.skip(skip).take(limit);

    try {
      // Thực hiện truy vấn
      const [items, total] = await query.getManyAndCount();
      this.logger.log(`Tìm thấy ${items.length}/${total} sản phẩm`);

      return {
        items,
        meta: {
          currentPage: page,
          itemsPerPage: limit,
          itemCount: items.length,
          totalItems: total,
          totalPages: Math.ceil(total / limit),
        },
      };
    } catch (error) {
      this.logger.error(`Lỗi khi tìm kiếm sản phẩm: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Tìm sản phẩm theo ID
   * @param id ID sản phẩm
   * @returns Sản phẩm hoặc null
   */
  async findProductById(id: number): Promise<CustomerProduct | null> {
    this.logger.log(`Tìm sản phẩm với ID: ${id}`);
    try {
      // Sử dụng findOne với các tùy chọn để đảm bảo lấy đầy đủ dữ liệu
      const product = await this.findOne({
        where: { id },
        select: [
          'id', 'name', 'price', 'typePrice', 'productType', 'description',
          'tags', 'userId', 'createdAt', 'updatedAt', 'status', 'customFields'
        ]
      });

      if (!product) {
        this.logger.log(`Không tìm thấy sản phẩm với ID: ${id}`);
        return null;
      }

      this.logger.log(`Đã tìm thấy sản phẩm: ${product.name}`);
      return product;
    } catch (error) {
      this.logger.error(`Lỗi khi tìm sản phẩm theo ID ${id}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Cập nhật trạng thái cho nhiều sản phẩm
   * @param productIds Danh sách ID sản phẩm
   * @param status Trạng thái mới
   * @param rejectReason Lý do từ chối (nếu có)
   * @returns Số lượng sản phẩm đã cập nhật
   */
  async updateProductsStatus(
    productIds: number[],
    status: EntityStatusEnum,
    rejectReason?: string,
  ): Promise<number> {
    this.logger.log(`Cập nhật trạng thái ${status} cho ${productIds.length} sản phẩm`);

    try {
      // Tạo object cập nhật
      const updateData: any = {
        status,
        updatedAt: Date.now(),
      };

      // Thêm lý do từ chối vào customFields nếu có
      if (rejectReason && status === EntityStatusEnum.REJECTED) {
        updateData.customFields = () => `
          CASE 
            WHEN custom_fields IS NULL THEN '{"rejectReason": "${rejectReason}"}'::jsonb
            ELSE custom_fields || '{"rejectReason": "${rejectReason}"}'::jsonb
          END
        `;
      }

      // Thực hiện cập nhật
      const result = await this.update(
        { id: In(productIds) },
        updateData
      );

      const updatedCount = result.affected || 0;
      this.logger.log(`Đã cập nhật trạng thái cho ${updatedCount} sản phẩm`);
      return updatedCount;
    } catch (error) {
      this.logger.error(`Lỗi khi cập nhật trạng thái sản phẩm: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Tìm sản phẩm theo danh sách ID
   * @param ids Danh sách ID sản phẩm
   * @returns Danh sách sản phẩm
   */
  async findProductsByIds(ids: number[]): Promise<CustomerProduct[]> {
    this.logger.log(`Tìm ${ids.length} sản phẩm theo danh sách ID`);

    try {
      const products = await this.find({
        where: { id: In(ids) },
        select: [
          'id', 'name', 'price', 'typePrice', 'productType', 'description',
          'tags', 'userId', 'createdAt', 'updatedAt', 'status', 'customFields'
        ]
      });

      this.logger.log(`Đã tìm thấy ${products.length} sản phẩm`);
      return products;
    } catch (error) {
      this.logger.error(`Lỗi khi tìm sản phẩm theo danh sách ID: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Đếm số lượng sản phẩm theo trạng thái
   * @param status Trạng thái cần đếm
   * @returns Số lượng sản phẩm
   */
  async countByStatus(status: EntityStatusEnum): Promise<number> {
    this.logger.log(`Đếm số lượng sản phẩm có trạng thái: ${status}`);

    try {
      const count = await this.count({
        where: { status }
      });

      this.logger.log(`Có ${count} sản phẩm với trạng thái ${status}`);
      return count;
    } catch (error) {
      this.logger.error(`Lỗi khi đếm sản phẩm theo trạng thái: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Đếm số lượng sản phẩm theo userId
   * @param userId ID người dùng
   * @returns Số lượng sản phẩm
   */
  async countByUserId(userId: number): Promise<number> {
    this.logger.log(`Đếm số lượng sản phẩm của người dùng: ${userId}`);

    try {
      const count = await this.count({
        where: { userId }
      });

      this.logger.log(`Người dùng ${userId} có ${count} sản phẩm`);
      return count;
    } catch (error) {
      this.logger.error(`Lỗi khi đếm sản phẩm theo userId: ${error.message}`, error.stack);
      throw error;
    }
  }
}
