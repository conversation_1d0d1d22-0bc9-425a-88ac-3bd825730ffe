/**
 * Interface cho thông tin chi tiết tồn kho trong field info (jsonb)
 * Chứa danh sách các batch/lot tồn kho với thông tin chi tiết
 */
export interface InventoryInfo {
  /**
   * Danh sách các batch/lot tồn kho
   */
  batches: InventoryBatch[];

  /**
   * Thông tin bổ sung khác
   */
  metadata?: {
    /**
     * Ghi chú về tồn kho
     */
    notes?: string;

    /**
     * Ngày kiểm kê cuối cùng
     */
    lastAuditDate?: number;

    /**
     * Người kiểm kê cuối cùng
     */
    lastAuditBy?: number;

    /**
     * Trạng thái tồn kho
     */
    status?: 'ACTIVE' | 'INACTIVE' | 'PENDING_AUDIT';

    /**
     * Thông tin bổ sung khác
     */
    [key: string]: any;
  };
}

/**
 * Interface cho từng batch/lot tồn kho
 */
export interface InventoryBatch {
  /**
   * ID của batch (unique trong phạm vi inventory)
   */
  batchId: string;

  /**
   * Mã SKU (Stock Keeping Unit) của sản phẩm trong batch này
   */
  sku?: string | null;

  /**
   * Mã vạch (Barcode) của sản phẩm trong batch này
   */
  barcode?: string | null;

  /**
   * Số lượng hiện tại trong batch này
   */
  currentQuantity: number;

  /**
   * Tổng số lượng đã nhập vào batch này (bao gồm cả đã xuất)
   */
  totalQuantity: number;

  /**
   * Số lượng sẵn sàng để bán hoặc sử dụng trong batch này
   */
  availableQuantity: number;

  /**
   * Số lượng bị giữ chỗ trong batch này (ví dụ: cho đơn hàng chưa hoàn tất)
   */
  reservedQuantity: number;

  /**
   * Số lượng sản phẩm lỗi hoặc không sử dụng được trong batch này
   */
  defectiveQuantity: number;

  /**
   * Ngày sản xuất (timestamp)
   */
  manufacturingDate?: number;

  /**
   * Ngày hết hạn (timestamp)
   */
  expiryDate?: number;

  /**
   * Số lô sản xuất
   */
  lotNumber?: string;

  /**
   * Giá nhập của batch này
   */
  costPrice?: {
    amount: number;
    currency: string;
  };

  /**
   * Vị trí trong kho
   */
  location?: {
    /**
     * Khu vực trong kho
     */
    zone?: string;

    /**
     * Dãy kệ
     */
    aisle?: string;

    /**
     * Kệ
     */
    shelf?: string;

    /**
     * Vị trí cụ thể
     */
    position?: string;
  };

  /**
   * Thông tin nhà cung cấp
   */
  supplier?: {
    /**
     * ID nhà cung cấp
     */
    supplierId?: number;

    /**
     * Tên nhà cung cấp
     */
    supplierName?: string;

    /**
     * Số hóa đơn nhập hàng
     */
    invoiceNumber?: string;
  };

  /**
   * Thời gian tạo batch (timestamp)
   */
  createdAt: number;

  /**
   * Thời gian cập nhật cuối cùng (timestamp)
   */
  updatedAt: number;

  /**
   * Trạng thái của batch
   */
  status: 'ACTIVE' | 'EXPIRED' | 'DAMAGED' | 'RECALLED';

  /**
   * Ghi chú về batch
   */
  notes?: string;

  /**
   * Thông tin bổ sung khác
   */
  metadata?: {
    [key: string]: any;
  };
}

/**
 * Type helper để tạo inventory info mới
 */
export type CreateInventoryInfoInput = {
  /**
   * Thông tin batch đầu tiên
   */
  initialBatch: Omit<InventoryBatch, 'batchId' | 'createdAt' | 'updatedAt' | 'status'> & {
    batchId?: string;
    status?: InventoryBatch['status'];
  };

  /**
   * Metadata cho inventory
   */
  metadata?: InventoryInfo['metadata'];
};

/**
 * Utility functions để làm việc với InventoryInfo
 */
export class InventoryInfoUtils {
  /**
   * Tạo inventory info mới với batch đầu tiên
   */
  static createInitialInfo(input: CreateInventoryInfoInput): InventoryInfo {
    const now = Date.now();
    const batchId = input.initialBatch.batchId || `batch_${now}_${Math.random().toString(36).substr(2, 9)}`;

    const batch: InventoryBatch = {
      ...input.initialBatch,
      batchId,
      createdAt: now,
      updatedAt: now,
      status: input.initialBatch.status || 'ACTIVE',
    };

    return {
      batches: [batch],
      metadata: {
        ...input.metadata,
        status: 'ACTIVE',
      },
    };
  }

  /**
   * Tính tổng số lượng từ tất cả các batch
   */
  static calculateTotalQuantities(info: InventoryInfo): {
    totalCurrentQuantity: number;
    totalAvailableQuantity: number;
    totalReservedQuantity: number;
    totalDefectiveQuantity: number;
  } {
    return info.batches.reduce(
      (totals, batch) => ({
        totalCurrentQuantity: totals.totalCurrentQuantity + batch.currentQuantity,
        totalAvailableQuantity: totals.totalAvailableQuantity + batch.availableQuantity,
        totalReservedQuantity: totals.totalReservedQuantity + batch.reservedQuantity,
        totalDefectiveQuantity: totals.totalDefectiveQuantity + batch.defectiveQuantity,
      }),
      {
        totalCurrentQuantity: 0,
        totalAvailableQuantity: 0,
        totalReservedQuantity: 0,
        totalDefectiveQuantity: 0,
      }
    );
  }

  /**
   * Thêm batch mới vào inventory info
   */
  static addBatch(info: InventoryInfo, batch: Omit<InventoryBatch, 'batchId' | 'createdAt' | 'updatedAt'>): InventoryInfo {
    const now = Date.now();
    const batchId = `batch_${now}_${Math.random().toString(36).substr(2, 9)}`;

    const newBatch: InventoryBatch = {
      ...batch,
      batchId,
      createdAt: now,
      updatedAt: now,
    };

    return {
      ...info,
      batches: [...info.batches, newBatch],
    };
  }

  /**
   * Cập nhật batch trong inventory info
   */
  static updateBatch(info: InventoryInfo, batchId: string, updates: Partial<InventoryBatch>): InventoryInfo {
    const now = Date.now();

    return {
      ...info,
      batches: info.batches.map(batch =>
        batch.batchId === batchId
          ? { ...batch, ...updates, updatedAt: now }
          : batch
      ),
    };
  }

  /**
   * Xóa batch khỏi inventory info
   */
  static removeBatch(info: InventoryInfo, batchId: string): InventoryInfo {
    return {
      ...info,
      batches: info.batches.filter(batch => batch.batchId !== batchId),
    };
  }
}
