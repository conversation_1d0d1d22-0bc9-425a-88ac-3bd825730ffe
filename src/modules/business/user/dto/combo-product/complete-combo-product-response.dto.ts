import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import {
  PriceTypeEnum,
  EntityStatusEnum,
  ProductTypeEnum
} from '@modules/business/enums';

/**
 * Response DTO cho combo item từ combo_products.combo_items
 */
export class ComboItemResponseDto {
  @ApiProperty({
    description: 'ID của sản phẩm trong combo',
    example: 123,
  })
  @Expose()
  productId: number;

  @ApiProperty({
    description: 'Số lượng sản phẩm trong combo',
    example: 2,
  })
  @Expose()
  total: number;
}

/**
 * Response DTO cho hình ảnh từ entity_has_media + media_data
 */
export class MediaResponseDto {
  @ApiProperty({
    description: 'Key của media (media_data.storage_key)',
    example: 'combo-image-0-1704067200000',
  })
  @Expose()
  key: string;

  @ApiProperty({
    description: 'ID của media (entity_has_media.media_id)',
    example: 'dcc8f803-04fc-49b3-953a-e660505ac353',
  })
  @Expose()
  mediaId: string;

  @ApiProperty({
    description: 'URL CDN của hình ảnh',
    example: 'https://cdn.redai.vn/images/combo-image-0-1704067200000.jpg',
  })
  @Expose()
  url: string;
}

/**
 * Response DTO cho customer_products table
 */
export class CustomerProductResponseDto {
  @ApiProperty({
    description: 'ID sản phẩm (customer_products.id)',
    example: 123,
  })
  @Expose()
  id: number;

  @ApiProperty({
    description: 'Tên sản phẩm (customer_products.name)',
    example: 'Combo Áo thun + Quần jean cao cấp',
  })
  @Expose()
  name: string;

  @ApiProperty({
    description: 'Mô tả sản phẩm (customer_products.description)',
    example: 'Combo thời trang nam gồm áo thun cotton và quần jean slim fit',
    required: false,
  })
  @Expose()
  description?: string;

  @ApiProperty({
    description: 'Giá bán chính thức của combo (customer_products.price) - Giá khách hàng thấy và thanh toán',
    example: {
      listPrice: 800000,
      salePrice: 650000,
      currency: 'VND'
    },
  })
  @Expose()
  price: any;

  @ApiProperty({
    description: 'Loại giá (customer_products.type_price)',
    enum: PriceTypeEnum,
    example: PriceTypeEnum.HAS_PRICE,
    nullable: true,
  })
  @Expose()
  typePrice: PriceTypeEnum | null;

  @ApiProperty({
    description: 'Tags (customer_products.tags)',
    type: [String],
    example: ['combo', 'thời trang', 'nam'],
    required: false,
  })
  @Expose()
  tags?: string[];

  @ApiProperty({
    description: 'Trạng thái (customer_products.status)',
    enum: EntityStatusEnum,
    example: EntityStatusEnum.APPROVED,
  })
  @Expose()
  status: EntityStatusEnum;

  @ApiProperty({
    description: 'Loại sản phẩm (customer_products.product_type)',
    enum: ProductTypeEnum,
    example: ProductTypeEnum.COMBO,
  })
  @Expose()
  productType: ProductTypeEnum;

  @ApiProperty({
    description: 'Thời gian tạo (customer_products.created_at)',
    example: 1704067200000,
  })
  @Expose()
  createdAt: number;

  @ApiProperty({
    description: 'Thời gian cập nhật (customer_products.updated_at)',
    example: 1704067200000,
  })
  @Expose()
  updatedAt: number;

  @ApiProperty({
    description: 'Custom fields (customer_products.custom_fields)',
    example: [
      {
        customFieldId: 1,
        value: { value: 'XL' }
      },
      {
        customFieldId: 2,
        value: { value: 'Đỏ' }
      }
    ],
    required: false,
  })
  @Expose()
  customFields?: Array<{
    customFieldId: number;
    value: { value: any };
  }>;
}

/**
 * Response DTO cho combo_products table
 */
export class ComboProductResponseDto {
  @ApiProperty({
    description: 'Giá tính toán từ tổng giá các sản phẩm con (combo_products.price) - Dùng để so sánh, phân tích tiết kiệm',
    example: {
      listPrice: 900000,
      salePrice: 750000,
      currency: 'VND',
      calculatedAt: 1704067200000,
      note: 'Giá tính toán từ tổng giá các sản phẩm con'
    },
  })
  @Expose()
  price: any;

  @ApiProperty({
    description: 'Danh sách sản phẩm trong combo (combo_products.combo_items)',
    type: [ComboItemResponseDto],
  })
  @Expose()
  @Type(() => ComboItemResponseDto)
  comboItems: ComboItemResponseDto[];

  @ApiProperty({
    description: 'Số lượng combo tối đa (combo_products.max_quantity)',
    example: 100,
    required: false,
  })
  @Expose()
  maxQuantity?: number;

  @ApiProperty({
    description: 'Thông tin tiết kiệm khi mua combo',
    example: {
      amount: 100000,
      percentage: 12,
      currency: 'VND',
      message: 'Tiết kiệm 100,000 VND (12%) khi mua combo'
    },
    required: false,
  })
  @Expose()
  savings?: {
    amount: number;
    percentage: number;
    currency: string;
    message: string;
  };
}

/**
 * Response DTO cho kết quả các thao tác
 */
export class ComboOperationResultsDto {
  @ApiProperty({
    description: 'Kết quả thao tác với product images (entity_has_media.product_id)',
    example: {
      added: [
        { entityMediaId: 'product-img-001', mediaId: 'dbadc742-60b9-4fac-969d-7330ed1daa5e', url: 'https://cdn.redai.vn/product-images/...' }
      ],
      deleted: [123]
    },
    required: false,
  })
  @Expose()
  productImages?: {
    added?: Array<{ entityMediaId: string; mediaId: string; url: string }>;
    deleted?: number[];
  };

  @ApiProperty({
    description: 'Kết quả thao tác với combo images (entity_has_media.product_combo_id)',
    example: {
      added: [
        { entityMediaId: 'combo-img-001', mediaId: 'cdfd4f5c-3cbd-49d7-b850-33b94c1a55a2', url: 'https://cdn.redai.vn/combo-images/...' }
      ],
      deleted: [456]
    },
    required: false,
  })
  @Expose()
  comboImages?: {
    added?: Array<{ entityMediaId: string; mediaId: string; url: string }>;
    deleted?: number[];
  };
}

/**
 * DTO hoàn chỉnh cho response của combo product
 * Bao gồm thông tin từ 3 bảng:
 * - customer_products table
 * - combo_products table
 * - entity_has_media table (với product_combo_id)
 */
export class CompleteComboProductResponseDto {
  // ========== CUSTOMER PRODUCTS TABLE ==========
  @ApiProperty({
    description: 'Thông tin từ customer_products table',
    type: CustomerProductResponseDto,
  })
  @Expose()
  @Type(() => CustomerProductResponseDto)
  customerProduct: CustomerProductResponseDto;

  // ========== COMBO PRODUCTS TABLE ==========
  @ApiProperty({
    description: 'Thông tin từ combo_products table',
    type: ComboProductResponseDto,
  })
  @Expose()
  @Type(() => ComboProductResponseDto)
  comboProduct: ComboProductResponseDto;

  // ========== ENTITY HAS MEDIA TABLE ==========
  @ApiProperty({
    description: 'Hình ảnh product level từ entity_has_media (product_id) - Ảnh chính của combo product',
    type: [MediaResponseDto],
    required: false,
  })
  @Expose()
  @Type(() => MediaResponseDto)
  productImages?: MediaResponseDto[];

  @ApiProperty({
    description: 'Hình ảnh combo level từ entity_has_media (product_combo_id) - Ảnh chi tiết combo',
    type: [MediaResponseDto],
    required: false,
  })
  @Expose()
  @Type(() => MediaResponseDto)
  comboImages?: MediaResponseDto[];

  // ========== KẾT QUẢ THAO TÁC ==========
  @ApiProperty({
    description: 'Kết quả các thao tác (chỉ có khi update)',
    type: ComboOperationResultsDto,
    required: false,
  })
  @Expose()
  @Type(() => ComboOperationResultsDto)
  operationResults?: ComboOperationResultsDto;
}
