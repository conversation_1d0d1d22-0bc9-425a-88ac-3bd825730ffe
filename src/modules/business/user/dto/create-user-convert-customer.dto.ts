import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional, IsString, IsObject, IsUUID, Matches, MaxLength, MinLength, ValidateIf, IsEmail, IsArray, ValidateNested, IsUrl } from 'class-validator';
import { Transform, Type } from 'class-transformer';
import { MetadataFieldDto } from './metadata-field.dto';

/**
 * DTO cho việc tạo khách hàng chuyển đổi mới
 */
export class CreateUserConvertCustomerDto {
  /**
   * Tên khách hàng
   * @example "Nguyễn Văn A"
   */
  @ApiProperty({
    description: 'Tên khách hàng',
    example: 'Nguyễn Văn A',
  })
  @IsNotEmpty({ message: 'Tên khách hàng không được để trống' })
  @IsString({ message: 'Tên khách hàng phải là chuỗi' })
  @MinLength(2, { message: 'Tên khách hàng phải có ít nhất 2 ký tự' })
  @MaxLength(255, { message: 'Tên khách hàng không được vượt quá 255 ký tự' })
  name: string;

  /**
   * Số điện thoại khách hàng (unique)
   * @example "0912345678"
   */
  @ApiProperty({
    description: 'Số điện thoại khách hàng (unique) - hỗ trợ định dạng quốc tế',
    example: '+84912345678',
  })
  @IsNotEmpty({ message: 'Số điện thoại không được để trống' })
  @IsString({ message: 'Số điện thoại phải là chuỗi' })
  @Matches(/^[\+]?[1-9][\d]{0,15}$/, {
    message: 'Số điện thoại không hợp lệ, phải là số điện thoại quốc tế hợp lệ',
  })
  phone: string;

  /**
   * Email khách hàng (có thể là string hoặc object JSON)
   * @example { "primary": "<EMAIL>", "secondary": "<EMAIL>" }
   * @example "<EMAIL>"
   */
  @ApiProperty({
    description: 'Email khách hàng (có thể là string hoặc object JSON)',
    example: { primary: '<EMAIL>', secondary: '<EMAIL>' },
    required: false,
    oneOf: [
      { type: 'string', format: 'email' },
      { type: 'object', additionalProperties: { type: 'string', format: 'email' } }
    ]
  })
  @IsOptional()
  @Transform(({ value }) => {
    // Nếu là string, chuyển thành object với key 'primary'
    if (typeof value === 'string') {
      return { primary: value };
    }
    return value;
  })
  @ValidateIf((o) => o.email !== undefined)
  @IsObject({ message: 'Email phải là chuỗi hoặc đối tượng JSON' })
  email?: Record<string, string> | string;



  /**
   * Thông tin file avatar để upload qua S3
   */
  @ApiProperty({
    description: 'Thông tin file avatar để upload qua S3',
    type: 'object',
    properties: {
      fileName: {
        type: 'string',
        description: 'Tên file avatar',
        example: 'avatar.jpg'
      },
      mimeType: {
        type: 'string',
        description: 'Loại MIME của file',
        example: 'image/jpeg',
        enum: ['image/jpeg', 'image/png', 'image/webp', 'image/gif']
      }
    },
  })
  @IsOptional()
  avatar?: {
    fileName: string;
    mimeType: string;
  };

  /**
   * Nền tảng nguồn (Facebook, Web,...)
   * @example "Facebook"
   */
  @ApiProperty({
    description: 'Nền tảng nguồn (Facebook, Web,...)',
    example: 'Facebook',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Nền tảng nguồn phải là chuỗi' })
  @MaxLength(50, { message: 'Nền tảng nguồn không được vượt quá 50 ký tự' })
  platform?: string;

  /**
   * Múi giờ của khách hàng
   * @example "Asia/Ho_Chi_Minh"
   */
  @ApiProperty({
    description: 'Múi giờ của khách hàng',
    example: 'Asia/Ho_Chi_Minh',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Múi giờ phải là chuỗi' })
  @MaxLength(50, { message: 'Múi giờ không được vượt quá 50 ký tự' })
  timezone?: string;

  /**
   * ID agent hỗ trợ khách hàng
   * @example "550e8400-e29b-41d4-a716-************"
   */
  @ApiProperty({
    description: 'ID agent hỗ trợ khách hàng',
    example: '550e8400-e29b-41d4-a716-************',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'ID agent phải là chuỗi' })
  @IsUUID('4', { message: 'ID agent phải là UUID hợp lệ' })
  agentId?: string;

  /**
   * Tags của khách hàng
   * @example ["vip", "potential", "interested"]
   */
  @ApiProperty({
    description: 'Tags của khách hàng',
    example: ['vip', 'potential', 'interested'],
    required: false,
  })
  @IsOptional()
  @IsArray({ message: 'Tags phải là mảng' })
  @IsString({ each: true, message: 'Mỗi tag phải là chuỗi' })
  tags?: string[];



  /**
   * Metadata - Dữ liệu trường tùy chỉnh (TÙY CHỌN)
   *
   * **Thay đổi quan trọng:**
   * - Chỉ cần gửi những custom fields bạn muốn điền
   * - KHÔNG bắt buộc phải điền tất cả các trường required
   * - Có thể gửi 1, vài, hoặc không gửi custom field nào
   * - Custom fields có thể được tạo bởi user hoặc employee
   * - Validation chỉ áp dụng cho những trường thực sự được gửi lên
   *
   * **Cách sử dụng:**
   * - Gửi mảng rỗng [] hoặc bỏ qua metadata: tạo khách hàng không có custom fields
   * - Gửi 1 item: tạo khách hàng với 1 custom field
   * - Gửi nhiều items: tạo khách hàng với nhiều custom fields
   *
   * @example [{ "configId": "day_of_birth", "value": "28/11/2003" }]
   */
  @ApiProperty({
    description: 'Metadata - Dữ liệu trường tùy chỉnh (TÙY CHỌN). Chỉ cần gửi những custom fields bạn muốn điền, không bắt buộc phải điền tất cả.',
    type: [MetadataFieldDto],
    required: false,
    example: [
      {
        configId: 'day_of_birth',
        value: '28/11/2003'
      },
      {
        configId: 'haianh',
        value: 'Thông tin bổ sung'
      },
      {
        configId: 'product_color',
        value: 'RED12345678'
      }
    ],
  })
  @IsOptional()
  @IsArray({ message: 'Metadata phải là mảng' })
  @ValidateNested({ each: true })
  @Type(() => MetadataFieldDto)
  metadata?: MetadataFieldDto[];

  /**
   * Link Facebook của khách hàng
   * @example "https://facebook.com/user123"
   */
  @ApiProperty({
    description: 'Link Facebook của khách hàng',
    example: 'https://facebook.com/user123',
    required: false,
    maxLength: 500,
  })
  @IsOptional()
  @IsString({ message: 'Link Facebook phải là chuỗi' })
  @IsUrl({}, { message: 'Link Facebook phải là URL hợp lệ' })
  @MaxLength(500, { message: 'Link Facebook không được vượt quá 500 ký tự' })
  facebookLink?: string;

  /**
   * Link Twitter của khách hàng
   * @example "https://twitter.com/user123"
   */
  @ApiProperty({
    description: 'Link Twitter của khách hàng',
    example: 'https://twitter.com/user123',
    required: false,
    maxLength: 500,
  })
  @IsOptional()
  @IsString({ message: 'Link Twitter phải là chuỗi' })
  @IsUrl({}, { message: 'Link Twitter phải là URL hợp lệ' })
  @MaxLength(500, { message: 'Link Twitter không được vượt quá 500 ký tự' })
  twitterLink?: string;

  /**
   * Link LinkedIn của khách hàng
   * @example "https://linkedin.com/in/user123"
   */
  @ApiProperty({
    description: 'Link LinkedIn của khách hàng',
    example: 'https://linkedin.com/in/user123',
    required: false,
    maxLength: 500,
  })
  @IsOptional()
  @IsString({ message: 'Link LinkedIn phải là chuỗi' })
  @IsUrl({}, { message: 'Link LinkedIn phải là URL hợp lệ' })
  @MaxLength(500, { message: 'Link LinkedIn không được vượt quá 500 ký tự' })
  linkedinLink?: string;

  /**
   * Link Zalo của khách hàng
   * @example "https://zalo.me/user123"
   */
  @ApiProperty({
    description: 'Link Zalo của khách hàng',
    example: 'https://zalo.me/user123',
    required: false,
    maxLength: 500,
  })
  @IsOptional()
  @IsString({ message: 'Link Zalo phải là chuỗi' })
  @IsUrl({}, { message: 'Link Zalo phải là URL hợp lệ' })
  @MaxLength(500, { message: 'Link Zalo không được vượt quá 500 ký tự' })
  zaloLink?: string;

  /**
   * Link Website của khách hàng
   * @example "https://example.com"
   */
  @ApiProperty({
    description: 'Link Website của khách hàng',
    example: 'https://example.com',
    required: false,
    maxLength: 500,
  })
  @IsOptional()
  @IsString({ message: 'Link Website phải là chuỗi' })
  @IsUrl({}, { message: 'Link Website phải là URL hợp lệ' })
  @MaxLength(500, { message: 'Link Website không được vượt quá 500 ký tự' })
  websiteLink?: string;

  /**
   * Địa chỉ khách hàng
   * @example "123 Đường ABC, Quận 1, TP.HCM"
   */
  @ApiProperty({
    description: 'Địa chỉ khách hàng',
    example: '123 Đường ABC, Quận 1, TP.HCM',
    required: false,
    maxLength: 500,
    nullable: true,
  })
  @IsOptional()
  @IsString({ message: 'Địa chỉ phải là chuỗi' })
  @MaxLength(500, { message: 'Địa chỉ không được vượt quá 500 ký tự' })
  address?: string | null;
}
