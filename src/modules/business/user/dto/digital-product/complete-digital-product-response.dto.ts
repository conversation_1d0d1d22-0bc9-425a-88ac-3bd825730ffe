import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { ProductTypeEnum, EntityStatusEnum, PriceTypeEnum } from '@modules/business/enums';
import { DigitalProductVersionResponseDto } from './digital-product-version-response.dto';

/**
 * Response DTO hoàn chỉnh cho digital product
 * <PERSON>o gồm tất cả thông tin từ customer_products + digital_products + versions + images
 */
export class CompleteDigitalProductResponseDto {
  // ========== THÔNG TIN TỪ CUSTOMER_PRODUCTS ==========
  
  @ApiProperty({
    description: 'ID sản phẩm',
    example: 123,
  })
  @Expose()
  id: number;

  @ApiProperty({
    description: 'Tên sản phẩm',
    example: 'Khóa học lập trình Python cơ bản',
  })
  @Expose()
  name: string;

  @ApiProperty({
    description: '<PERSON>ô tả sản phẩm',
    example: '<PERSON>h<PERSON><PERSON> học lập trình Python từ cơ bản đến nâng cao với nhiều bài tập thực hành',
    required: false,
  })
  @Expose()
  description?: string;

  @ApiProperty({
    description: 'Loại sản phẩm',
    enum: ProductTypeEnum,
    example: ProductTypeEnum.DIGITAL,
  })
  @Expose()
  productType: ProductTypeEnum;

  @ApiProperty({
    description: 'Giá sản phẩm',
    example: {
      listPrice: 500000,
      salePrice: 450000,
      currency: 'VND'
    },
    required: false,
  })
  @Expose()
  price?: any;

  @ApiProperty({
    description: 'Kiểu giá sản phẩm',
    enum: PriceTypeEnum,
    example: PriceTypeEnum.HAS_PRICE,
    required: false,
    nullable: true,
  })
  @Expose()
  typePrice?: PriceTypeEnum | null;

  @ApiProperty({
    description: 'Danh sách tags sản phẩm',
    type: [String],
    example: ['khóa học', 'lập trình', 'python', 'online'],
    required: false,
  })
  @Expose()
  tags?: string[];

  @ApiProperty({
    description: 'Trạng thái sản phẩm',
    enum: EntityStatusEnum,
    example: EntityStatusEnum.APPROVED,
  })
  @Expose()
  status: EntityStatusEnum;

  @ApiProperty({
    description: 'Custom fields',
    example: [
      {
        customFieldId: 1,
        value: { value: 'Tiếng Việt' }
      },
      {
        customFieldId: 2,
        value: { value: 'PDF' }
      }
    ],
    required: false,
  })
  @Expose()
  customFields?: any[];

  @ApiProperty({
    description: 'Thời gian tạo',
    example: '2024-01-15T10:30:00.000Z',
    nullable: true,
  })
  @Expose()
  createdAt: Date | null;

  @ApiProperty({
    description: 'Thời gian cập nhật cuối',
    example: '2024-01-15T10:30:00.000Z',
    nullable: true,
  })
  @Expose()
  updatedAt: Date | null;

  @ApiProperty({
    description: 'ID người tạo sản phẩm',
    example: 1,
  })
  @Expose()
  userId: number;

  // ========== THÔNG TIN TỪ DIGITAL_PRODUCTS ==========

  @ApiProperty({
    description: 'Phương thức giao hàng',
    example: 'Tải xuống trực tiếp',
    required: false,
  })
  @Expose()
  deliveryMethod?: string;

  @ApiProperty({
    description: 'Thời điểm giao hàng',
    example: 'Ngay lập tức sau khi thanh toán',
    required: false,
  })
  @Expose()
  deliveryTime?: string;

  @ApiProperty({
    description: 'Thời gian chờ trước khi giao hàng',
    example: 'Xử lý trong 5 phút',
    required: false,
  })
  @Expose()
  waitingTime?: string;

  // ========== THÔNG TIN 1:MANY RELATIONSHIPS ==========

  @ApiProperty({
    description: 'Danh sách phiên bản sản phẩm số',
    type: [DigitalProductVersionResponseDto],
    required: false,
  })
  @Expose()
  @Type(() => DigitalProductVersionResponseDto)
  versions?: DigitalProductVersionResponseDto[];

  @ApiProperty({
    description: 'Danh sách hình ảnh sản phẩm (product level)',
    example: [
      {
        id: '123e4567-e89b-12d3-a456-426614174000',
        key: 'business/IMAGE/2025/06/product-image-1.jpg',
        url: 'https://cdn.redai.vn/business/IMAGE/2025/06/product-image-1.jpg?expires=1750214285&signature=abc123',
        position: 1,
        mimeType: 'image/jpeg',
        name: 'Product Image 1',
        size: '0'
      },
      {
        id: '456e7890-e89b-12d3-a456-426614174001',
        key: 'business/IMAGE/2025/06/product-image-2.jpg',
        url: 'https://cdn.redai.vn/business/IMAGE/2025/06/product-image-2.jpg?expires=1750214285&signature=def456',
        position: 2,
        mimeType: 'image/png',
        name: 'Product Image 2',
        size: '0'
      }
    ],
    required: false,
  })
  @Expose()
  images?: Array<{
    id: string;
    key: string;
    url: string;
    position: number;
    mimeType: string;
    name: string;
    size: string;
  }>;

  // ========== UPLOAD URLs (CHỈ CÓ KHI CẬP NHẬT) ==========

  @ApiProperty({
    description: 'Danh sách URL upload cho hình ảnh sản phẩm mới (chỉ có khi có ADD operations)',
    example: [
      {
        url: 'https://redaivn.hn.ss.bfcplatform.vn/business/IMAGE/2025/06/1750129189687-1c166316-c074-4ea9-9d5f-087ea4fa5628?X-Amz-Algorithm=AWS4-HMAC-SHA256...',
        key: 'business/IMAGE/2025/06/1750129189687-1c166316-c074-4ea9-9d5f-087ea4fa5628',
        type: 'image/jpeg',
        mediaId: '63e58e32-0c29-4338-b738-a66698b47908',
        mediaLinkId: '27',
        name: 'Product Image 1750129189687'
      }
    ],
    required: false,
  })
  @Expose()
  imageUploadUrls?: Array<{
    url: string;
    key: string;
    type: string;
    mediaId: string;
    mediaLinkId: string;
    name: string;
  }>;

  @ApiProperty({
    description: 'Danh sách URL upload cho hình ảnh phiên bản mới (chỉ có khi có version image ADD operations)',
    example: [
      {
        url: 'https://redaivn.hn.ss.bfcplatform.vn/business/IMAGE/2025/06/1750129189152-c055d8a4-6d6d-4824-ac96-0b02db662bf4?X-Amz-Algorithm=AWS4-HMAC-SHA256...',
        key: 'business/IMAGE/2025/06/1750129189152-c055d8a4-6d6d-4824-ac96-0b02db662bf4',
        type: 'image/jpeg',
        mediaId: '5a24a0a8-2aa3-4c87-b77f-5a4d77ecc8fa',
        mediaLinkId: '25',
        name: 'Version Image 1750129189152',
        versionId: '15'
      }
    ],
    required: false,
  })
  @Expose()
  versionImageUploadUrls?: Array<{
    url: string;
    key: string;
    type: string;
    mediaId: string;
    mediaLinkId: string;
    name: string;
    versionId: string;
  }>;
}
