import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNumber, IsOptional, IsString } from 'class-validator';
import { Type } from 'class-transformer';
import { PriceTypeEnum, ProductTypeEnum } from '@modules/business/enums';
import { SortDirection } from '@common/dto/query.dto';

/**
 * Enum cho các trường sắp xếp sản phẩm
 */
export enum ProductSortField {
  NAME = 'name',
  CREATED_AT = 'createdAt',
  UPDATED_AT = 'updatedAt',
}

/**
 * DTO cho các tham số truy vấn danh sách sản phẩm
 */
export class QueryProductDto {
  @ApiProperty({
    description: 'Trang hiện tại',
    example: 1,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  page?: number = 1;

  @ApiProperty({
    description: 'Số lượng sản phẩm trên một trang',
    example: 10,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  limit?: number = 10;

  @ApiProperty({
    description: 'Từ khóa tìm kiếm theo tên sản phẩm',
    example: 'áo thun',
    required: false,
  })
  @IsOptional()
  @IsString()
  search?: string;

  // Đã loại bỏ trường createdBy theo yêu cầu

  @ApiProperty({
    description: 'Loại giá',
    enum: PriceTypeEnum,
    example: PriceTypeEnum.HAS_PRICE,
    required: false,
  })
  @IsOptional()
  @IsEnum(PriceTypeEnum)
  typePrice?: PriceTypeEnum;

  @ApiProperty({
    description: 'Loại sản phẩm',
    enum: ProductTypeEnum,
    example: ProductTypeEnum.DIGITAL,
    required: false,
  })
  @IsOptional()
  @IsEnum(ProductTypeEnum)
  @Type(() => String)
  productType?: ProductTypeEnum;

  @ApiProperty({
    description: 'Tags sản phẩm (phân tách bằng dấu phẩy)',
    example: 'áo thun,nam,cotton',
    required: false,
  })
  @IsOptional()
  @IsString()
  tags?: string;

  @ApiProperty({
    description: 'Trường sắp xếp',
    enum: ProductSortField,
    default: ProductSortField.CREATED_AT,
    required: false,
  })
  @IsOptional()
  @IsEnum(ProductSortField)
  sortBy?: ProductSortField = ProductSortField.CREATED_AT;

  @ApiProperty({
    description: 'Hướng sắp xếp',
    enum: SortDirection,
    default: SortDirection.DESC,
    required: false,
  })
  @IsOptional()
  @IsEnum(SortDirection)
  sortDirection?: SortDirection = SortDirection.DESC;

  // Service-specific filters
  @ApiProperty({
    description: 'Lọc theo nhà cung cấp dịch vụ - Chỉ áp dụng cho sản phẩm SERVICE',
    example: 'Công ty tư vấn ABC',
    required: false,
  })
  @IsOptional()
  @IsString()
  serviceProvider?: string;

  @ApiProperty({
    description: 'Lọc theo loại dịch vụ - Chỉ áp dụng cho sản phẩm SERVICE',
    example: 'CONSULTATION',
    enum: ['CONSULTATION', 'TRAINING', 'SUPPORT', 'MAINTENANCE', 'OTHER'],
    required: false,
  })
  @IsOptional()
  @IsString()
  serviceType?: string;

  @ApiProperty({
    description: 'Lọc theo địa điểm thực hiện dịch vụ - Chỉ áp dụng cho sản phẩm SERVICE',
    example: 'AT_CENTER',
    enum: ['AT_CENTER', 'AT_CUSTOMER', 'ONLINE', 'HYBRID'],
    required: false,
  })
  @IsOptional()
  @IsString()
  serviceLocation?: string;
}
