import { ApiProperty } from '@nestjs/swagger';
import {
  IsArray,
  IsEnum,
  IsNotEmpty,
  IsOptional,
  IsString,
  MaxLength,
  ValidateNested,
} from 'class-validator';
import { CustomFieldInputDto } from '../../custom-field-metadata.dto';
import { Type } from 'class-transformer';
import { ProductTypeEnum } from '@/modules/business/enums';

/**
 * Base DTO chung cho tất cả các loại sản phẩm
 * Chứa các trường dữ liệu chung mà mọi sản phẩm đều có
 */
export abstract class BaseProductDto {
  @ApiProperty({
    description: 'Loại sản phẩm - luôn là PHYSICAL',
    enum: ProductTypeEnum,
    example: ProductTypeEnum.PHYSICAL,
  })
  @IsEnum(ProductTypeEnum)
  @IsNotEmpty()
  productType: ProductTypeEnum;

  @ApiProperty({
    description: 'Tên sản phẩm',
    example: '<PERSON><PERSON> thun nam',
    maxLength: 255,
  })
  @IsString()
  @IsNotEmpty()
  @MaxLength(255)
  name: string;

  @ApiProperty({
    description: 'Mô tả sản phẩm',
    example: 'Áo thun nam chất liệu cotton cao cấp',
    required: false,
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({
    description: 'Danh sách loại hình ảnh sản phẩm',
    type: [String],
    example: ['image/jpeg', 'image/png'],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  imagesMediaTypes?: string[];

  @ApiProperty({
    description: 'Danh sách tags sản phẩm',
    type: [String],
    example: ['thời trang', 'nam', 'cotton'],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];

  @ApiProperty({
    description: 'Danh sách custom fields',
    type: [CustomFieldInputDto],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CustomFieldInputDto)
  customFields?: CustomFieldInputDto[];
}
