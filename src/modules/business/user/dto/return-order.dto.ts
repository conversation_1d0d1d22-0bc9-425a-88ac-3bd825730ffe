import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsNumber, IsOptional, IsString, MaxLength } from 'class-validator';

/**
 * DTO cho request hoàn đơn hàng
 */
export class ReturnOrderRequestDto {
  @ApiProperty({
    description: 'ID đơn hàng cần hoàn',
    example: 123,
    type: Number
  })
  @IsNotEmpty()
  @IsNumber()
  orderId: number;

  @ApiProperty({
    description: 'Lý do hoàn đơn hàng',
    example: 'Hàng bị hỏng khi giao',
    required: false,
    maxLength: 500
  })
  @IsOptional()
  @IsString()
  @MaxLength(500, { message: 'Lý do hoàn đơn không được vượt quá 500 ký tự' })
  reason?: string;
}

/**
 * DTO cho response hoàn đơn hàng
 */
export class ReturnOrderResponseDto {
  @ApiProperty({
    description: 'ID đơn hàng đã hoàn',
    example: 123
  })
  orderId: number;

  @ApiProperty({
    description: 'Mã vận đơn',
    example: 'GHN123456789'
  })
  trackingNumber: string;

  @ApiProperty({
    description: 'Đơn vị vận chuyển',
    enum: ['GHN', 'GHTK'],
    example: 'GHN'
  })
  carrier: string;

  @ApiProperty({
    description: 'Trạng thái hoàn đơn',
    example: true
  })
  success: boolean;

  @ApiProperty({
    description: 'Thông tin phản hồi từ nhà vận chuyển',
    type: 'object',
    additionalProperties: true,
    example: {
      message: 'Yêu cầu hoàn đơn thành công',
      code: 200
    }
  })
  carrierResponse: any;

  @ApiProperty({
    description: 'Thời gian yêu cầu hoàn đơn (timestamp)',
    example: 1705394400000
  })
  returnedAt: number;

  @ApiProperty({
    description: 'Lý do hoàn đơn',
    example: 'Hàng bị hỏng khi giao',
    required: false
  })
  @IsOptional()
  reason?: string;

  @ApiProperty({
    description: 'Ghi chú bổ sung',
    example: 'Đơn hàng đã được yêu cầu hoàn trả thành công',
    required: false
  })
  @IsOptional()
  notes?: string;
}

/**
 * DTO cho request giao lại đơn hàng (chỉ GHN)
 */
export class DeliveryAgainOrderRequestDto {
  @ApiProperty({
    description: 'ID đơn hàng cần giao lại',
    example: 123,
    type: Number
  })
  @IsNotEmpty()
  @IsNumber()
  orderId: number;

  @ApiProperty({
    description: 'Ghi chú cho việc giao lại',
    example: 'Khách hàng yêu cầu giao lại vào buổi chiều',
    required: false,
    maxLength: 500
  })
  @IsOptional()
  @IsString()
  @MaxLength(500, { message: 'Ghi chú không được vượt quá 500 ký tự' })
  notes?: string;
}

/**
 * DTO cho response giao lại đơn hàng
 */
export class DeliveryAgainOrderResponseDto {
  @ApiProperty({
    description: 'ID đơn hàng giao lại',
    example: 123
  })
  orderId: number;

  @ApiProperty({
    description: 'Mã vận đơn',
    example: 'GHN123456789'
  })
  trackingNumber: string;

  @ApiProperty({
    description: 'Đơn vị vận chuyển (chỉ GHN hỗ trợ)',
    example: 'GHN'
  })
  carrier: string;

  @ApiProperty({
    description: 'Trạng thái yêu cầu giao lại',
    example: true
  })
  success: boolean;

  @ApiProperty({
    description: 'Thông tin phản hồi từ GHN',
    type: 'object',
    additionalProperties: true,
    example: {
      message: 'Yêu cầu giao lại thành công',
      code: 200
    }
  })
  carrierResponse: any;

  @ApiProperty({
    description: 'Thời gian yêu cầu giao lại (timestamp)',
    example: 1705394400000
  })
  deliveryAgainAt: number;

  @ApiProperty({
    description: 'Ghi chú',
    example: 'Khách hàng yêu cầu giao lại vào buổi chiều',
    required: false
  })
  @IsOptional()
  notes?: string;
}
