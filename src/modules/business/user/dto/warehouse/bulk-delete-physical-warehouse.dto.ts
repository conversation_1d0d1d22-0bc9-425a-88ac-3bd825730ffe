import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsNotEmpty, IsNumber, ArrayMinSize, ArrayUnique } from 'class-validator';
import { Type } from 'class-transformer';

/**
 * DTO cho yêu cầu xóa nhiều kho vật lý
 */
export class BulkDeletePhysicalWarehouseDto {
  /**
   * Danh sách ID kho vật lý cần xóa
   * @example [1, 2, 3]
   */
  @ApiProperty({
    description: 'Danh sách ID kho vật lý cần xóa',
    example: [1, 2, 3],
    type: [Number],
  })
  @IsArray({ message: 'Danh sách ID kho phải là mảng' })
  @ArrayMinSize(1, { message: 'Phải có ít nhất một kho để xóa' })
  @ArrayUnique({ message: 'Danh sách ID kho không được trùng lặp' })
  @IsNumber({}, { each: true, message: 'ID kho phải là số' })
  @Type(() => Number)
  @IsNotEmpty({ message: '<PERSON><PERSON> sách ID kho không được để trống' })
  warehouseIds: number[];
}
