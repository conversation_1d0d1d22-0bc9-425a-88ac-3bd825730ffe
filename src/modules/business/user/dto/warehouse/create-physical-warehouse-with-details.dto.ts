import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsNumber, IsOptional, IsString, MaxLength, IsArray, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
import { CustomFieldInputDto } from '../custom-field-metadata.dto';

/**
 * DTO cho việc tạo kho vật lý kèm thông tin chi tiết
 * Gộp thông tin từ CreateWarehouseDto và CreatePhysicalWarehouseDto
 */
export class CreatePhysicalWarehouseWithDetailsDto {

  /**
   * Tên kho
   * @example "Kho hàng chính"
   */
  @ApiProperty({
    description: 'Tên kho',
    example: 'Kho hàng chính',
    maxLength: 100,
  })
  @IsNotEmpty({ message: 'Tên kho không được để trống' })
  @IsString({ message: 'Tên kho phải là chuỗi' })
  @MaxLength(100, { message: 'Tên kho không được vượt quá 100 ký tự' })
  name: string;

  /**
   * <PERSON>ô tả kho
   * @example "Kho chứa các sản phẩm chính của công ty"
   */
  @ApiProperty({
    description: 'Mô tả kho',
    example: 'Kho chứa các sản phẩm chính của công ty',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Mô tả kho phải là chuỗi' })
  description?: string;

  /**
   * Địa chỉ kho
   * @example "123 Đường ABC, Quận 1, TP.HCM"
   */
  @ApiProperty({
    description: 'Địa chỉ kho',
    example: '123 Đường ABC, Quận 1, TP.HCM',
    maxLength: 255,
  })
  @IsNotEmpty({ message: 'Địa chỉ kho không được để trống' })
  @IsString({ message: 'Địa chỉ kho phải là chuỗi' })
  @MaxLength(255, { message: 'Địa chỉ kho không được vượt quá 255 ký tự' })
  address: string;

  /**
   * Sức chứa kho
   * @example 1000
   */
  @ApiProperty({
    description: 'Sức chứa kho',
    example: 1000,
    required: false,
  })
  @IsOptional()
  @IsNumber({}, { message: 'Sức chứa kho phải là số' })
  @Type(() => Number)
  capacity?: number;

  /**
   * Danh sách custom fields cho kho
   * @example [{"customFieldId": 1, "value": {"value": "Giá trị mẫu"}}]
   */
  @ApiProperty({
    description: 'Danh sách custom fields cho kho',
    type: [CustomFieldInputDto],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CustomFieldInputDto)
  customFields?: CustomFieldInputDto[];
}
