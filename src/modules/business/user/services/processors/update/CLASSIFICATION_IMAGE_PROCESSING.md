# Cập Nhật Xử Lý Ảnh cho Classifications

## 🎯 Tổng Quan

Đã **cập nhật ClassificationService** để xử lý `imageOperations` với chức năng **xóa file thực sự trên S3** khi có DELETE operation, tương tự như logic trong UpdateProductOrchestrator.

## ✅ Đã Cập Nhật

### 1. **Method `create()` - Tạo Classification Mới**

**Cập nhật logic DELETE:**
```typescript
// Xóa file trên S3 trước khi xóa khỏi database
await this.s3Service.deleteFile(deleteOperation.key);
this.logger.log(`Successfully deleted classification image from S3: ${deleteOperation.key}`);

// Xóa khỏi finalImages
finalImages = finalImages.filter((img) => img.key !== deleteOperation.key);
```

### 2. **Method `update()` - Cập Nhật Classification**

**Cập nhật logic DELETE:**
```typescript
// Xóa file trên S3 trước khi xóa khỏi database
await this.s3Service.deleteFile(deleteOp.key);
this.logger.log(`Successfully deleted classification image from S3: ${deleteOp.key}`);

// Xóa khỏi database
images = images.filter((img) => img.key !== deleteOp.key);
```

### 3. **Xử Lý Cả 2 Format**

- **imageOperations** (format mới) - Đã cập nhật
- **imagesMediaTypes với operations** (deprecated) - Đã cập nhật

## 🔧 Tính Năng Mới

### **DELETE Operation**
- ✅ Xóa file thực sự trên S3 bằng `s3Service.deleteFile()`
- ✅ Xóa key khỏi classification metadata
- ✅ Hỗ trợ xóa theo `key` hoặc `position`
- ✅ Error handling graceful (tiếp tục nếu S3 delete thất bại)

### **ADD Operation**
- ✅ Tạo S3 key với pattern: `classification-{id}-image-{index}-{timestamp}`
- ✅ Generate presigned URL cho upload
- ✅ Tự động tính position cho ảnh mới

## 📊 Luồng Xử Lý

```
Classification Update Request
        ↓
1. Validate Classification
        ↓
2. Process imageOperations
   ├── DELETE: Xóa file S3 + xóa key database
   └── ADD: Tạo S3 key + presigned URL
        ↓
3. Update Metadata
        ↓
4. Save Classification
        ↓
5. Return Response với Upload URLs
```

## 🎯 Cách Sử Dụng

### **Request Example:**
```json
{
  "id": 456,
  "type": "Màu sắc",
  "imageOperations": [
    {
      "operation": "DELETE",
      "key": "business/IMAGE/2025/06/classification-456-image-0-1234567890.jpg"
    },
    {
      "operation": "ADD",
      "mimeType": "image/png"
    }
  ]
}
```

### **Response Example:**
```json
{
  "id": 456,
  "type": "Màu sắc",
  "uploadUrls": {
    "classificationId": 456,
    "imagesUploadUrls": [
      {
        "url": "https://presigned-url...",
        "key": "business/IMAGE/2025/06/classification-456-image-1-1234567890.png",
        "index": 1
      }
    ]
  }
}
```

## 🔍 Logging

**DELETE Operations:**
```
[ClassificationService] Successfully deleted classification image from S3: business/IMAGE/2025/06/classification-456-image-0-1234567890.jpg
[ClassificationService] Removed classification image key from database: business/IMAGE/2025/06/classification-456-image-0-1234567890.jpg
```

**ADD Operations:**
```
[ClassificationService] Created presigned URL for classification image upload: business/IMAGE/2025/06/classification-456-image-1-1234567890.png with position 1
```

## ⚠️ Error Handling

### **S3 Delete Failure:**
- Log warning nhưng tiếp tục xử lý
- Không throw exception để tránh rollback transaction
- Database vẫn được cập nhật

### **S3 Upload URL Generation Failure:**
- Log error chi tiết
- Skip operation cụ thể
- Tiếp tục với operations khác

## 🚀 Tích Hợp với UpdateProduct

**Classifications được xử lý trong UpdateProductOrchestrator:**
```typescript
// BƯỚC 7: Process Classifications
const classificationUploadUrls = await this.processClassificationsUpdate(
  product,
  updateProductDto,
  userId,
);
```

**ClassificationService.update() được gọi với imageOperations:**
- Xử lý DELETE: Xóa file S3 + database
- Xử lý ADD: Tạo presigned URLs
- Return upload URLs cho response

## 📈 Performance

- **Async S3 Operations**: Tất cả S3 calls đều async
- **Graceful Degradation**: Tiếp tục nếu S3 operations thất bại
- **Batch Processing**: Xử lý multiple operations trong 1 request
- **Memory Efficient**: Không load file content, chỉ xử lý keys

## 🧪 Testing

**Test DELETE Operation:**
```bash
curl -X PUT /business/user/classifications/456 \
  -d '{
    "imageOperations": [
      {
        "operation": "DELETE",
        "key": "business/IMAGE/2025/06/classification-456-image-0-1234567890.jpg"
      }
    ]
  }'
```

**Test ADD Operation:**
```bash
curl -X PUT /business/user/classifications/456 \
  -d '{
    "imageOperations": [
      {
        "operation": "ADD",
        "mimeType": "image/png"
      }
    ]
  }'
```

## 📚 Related Files

- `classification.service.ts` - Main implementation
- `UpdateClassificationDto` - Request DTO với imageOperations
- `ClassificationResponseDto` - Response với uploadUrls
- `update-product-orchestrator.ts` - Integration point
- `S3Service.deleteFile()` - S3 deletion method

## ✨ Kết Luận

Classifications giờ đây có **đầy đủ chức năng xử lý ảnh** tương tự như main product images:
- ✅ DELETE: Xóa file thực sự trên S3
- ✅ ADD: Tạo presigned URLs cho upload
- ✅ Error handling robust
- ✅ Logging chi tiết
- ✅ Tích hợp với UpdateProduct workflow
