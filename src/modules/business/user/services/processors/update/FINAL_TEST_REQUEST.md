# Final Test Request cho Classification ImageOperations

## 🧪 Test Request

```bash
curl -X PUT http://localhost:3000/v1/user/products/344 \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "name": "Test Classification ImageOperations",
    "classifications": [
      {
        "id": 456,
        "type": "Màu sắc",
        "imageOperations": [
          {
            "operation": "ADD",
            "mimeType": "image/png"
          }
        ]
      }
    ]
  }'
```

## ✅ Expected Success Response

```json
{
  "id": 344,
  "name": "Test Classification ImageOperations",
  "classifications": [
    {
      "id": 456,
      "type": "<PERSON><PERSON>u sắc",
      "uploadUrls": {
        "classificationId": 456,
        "imagesUploadUrls": [
          {
            "url": "https://presigned-url-for-classification...",
            "key": "business/IMAGE/2025/06/classification-456-image-0-**********.png",
            "index": 0
          }
        ]
      }
    }
  ],
  "classificationUploadUrls": [
    {
      "classificationId": 456,
      "imagesUploadUrls": [
        {
          "url": "https://presigned-url-for-classification...",
          "key": "business/IMAGE/2025/06/classification-456-image-0-**********.png",
          "index": 0
        }
      ]
    }
  ]
}
```

## 🔧 Implementation Summary

### ✅ Completed Changes

1. **DTO Updates:**
   - ✅ Created `ClassificationImageOperationDto` class
   - ✅ Added `imageOperations` field to `UpdateClassificationDto`
   - ✅ Added proper validation decorators
   - ✅ Added to `@ApiExtraModels` in `BusinessUpdateProductDto`

2. **Service Logic:**
   - ✅ Updated `ClassificationService.update()` to handle DELETE with S3 file deletion
   - ✅ Updated `ClassificationService.create()` to handle DELETE with S3 file deletion
   - ✅ Both methods handle ADD operations with presigned URL generation

3. **Processor Integration:**
   - ✅ Updated `UpdateProductProcessor.processClassificationsUpdate()` to pass `imageOperations`
   - ✅ Added logic to collect upload URLs from classification responses
   - ✅ Proper TypeScript casting for accessing `imageOperations`

### 🎯 Key Features

- **DELETE Operations**: 
  - Xóa file thực sự trên S3 bằng `s3Service.deleteFile()`
  - Xóa key khỏi classification metadata
  - Hỗ trợ xóa theo `key` hoặc `position`
  - Graceful error handling (tiếp tục nếu S3 delete thất bại)

- **ADD Operations**:
  - Tạo S3 key với pattern: `classification-{id}-image-{index}-{timestamp}`
  - Generate presigned URL với 15 phút expiry
  - Tự động tính position cho ảnh mới

- **Validation**:
  - Operation chỉ chấp nhận 'ADD' hoặc 'DELETE'
  - MIME type chỉ chấp nhận image types hợp lệ
  - Proper nested validation với `@ValidateNested()`

### 📊 Validation Flow

```
Request → CustomValidationPipe → BusinessUpdateProductDto
    ↓
classifications[] → UpdateClassificationDto
    ↓
imageOperations[] → ClassificationImageOperationDto
    ↓
Validation Success → Service Processing
```

### 🔍 Debugging

Nếu vẫn gặp lỗi validation, check:

1. **Import Issues**: Đảm bảo tất cả DTOs được import đúng
2. **ApiExtraModels**: Đảm bảo tất cả nested DTOs được khai báo
3. **Type Decorators**: Đảm bảo `@Type(() => ClassificationImageOperationDto)` đúng
4. **Validation Pipeline**: Check CustomValidationPipe settings

### 🚀 Ready to Test

- API endpoint: `PUT /v1/user/products/{id}`
- Request body: Include `classifications` with `imageOperations`
- Expected: No validation errors, proper S3 operations

## 📝 Logs Expected

```
[UpdateProductProcessor] Cập nhật 1 classifications cho sản phẩm 344
[ClassificationService] Created presigned URL for classification image upload: business/IMAGE/2025/06/classification-456-image-0-**********.png with position 0
[UpdateProductProcessor] Thu thập upload URLs từ classification 456
```

## 🎉 Success Criteria

- ✅ No validation errors (code 9003)
- ✅ `imageOperations` field accepted in request
- ✅ ADD operations return presigned URLs
- ✅ DELETE operations delete S3 files
- ✅ Response includes `classificationUploadUrls`
