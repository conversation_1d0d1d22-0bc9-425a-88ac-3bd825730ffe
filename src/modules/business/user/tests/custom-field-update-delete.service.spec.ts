import { Test, TestingModule } from '@nestjs/testing';
import { CustomFieldService } from '../services';
import { CustomFieldRepository, CustomGroupFormRepository, CustomGroupFormFieldRepository } from '@modules/business/repositories';
import { ValidationHelper } from '../helpers';
import { UpdateCustomFieldDto } from '../dto';
import { CustomField, CustomGroupFormField } from '@modules/business/entities';
import { EntityStatusEnum } from '@modules/business/enums';
import { AppException } from '@common/exceptions/app.exception';
import { BUSINESS_ERROR_CODES } from '@modules/business/exceptions';
import { DEFAULT_COMPONENTS } from '@modules/business/constants/component.constants';

describe('CustomFieldService - Update, Delete, Components', () => {
  let service: CustomFieldService;
  let customFieldRepository: CustomFieldRepository;
  let customGroupFormRepository: CustomGroupFormRepository;
  let customGroupFormFieldRepository: CustomGroupFormFieldRepository;
  let validationHelper: ValidationHelper;

  const mockCustomFieldRepository = {
    findOne: jest.fn(),
    findByIdAndNotDeleted: jest.fn(),
    find: jest.fn(),
    save: jest.fn(),
  };

  const mockCustomGroupFormRepository = {
    findById: jest.fn(),
  };

  const mockCustomGroupFormFieldRepository = {
    find: jest.fn(),
    remove: jest.fn(),
  };

  const mockValidationHelper = {
    validateCustomFieldExists: jest.fn(),
    validateUpdateCustomFieldData: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CustomFieldService,
        {
          provide: CustomFieldRepository,
          useValue: mockCustomFieldRepository,
        },
        {
          provide: CustomGroupFormRepository,
          useValue: mockCustomGroupFormRepository,
        },
        {
          provide: CustomGroupFormFieldRepository,
          useValue: mockCustomGroupFormFieldRepository,
        },
        {
          provide: ValidationHelper,
          useValue: mockValidationHelper,
        },
      ],
    }).compile();

    service = module.get<CustomFieldService>(CustomFieldService);
    customFieldRepository = module.get<CustomFieldRepository>(CustomFieldRepository);
    customGroupFormRepository = module.get<CustomGroupFormRepository>(CustomGroupFormRepository);
    customGroupFormFieldRepository = module.get<CustomGroupFormFieldRepository>(CustomGroupFormFieldRepository);
    validationHelper = module.get<ValidationHelper>(ValidationHelper);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('update', () => {
    it('should update a custom field successfully', async () => {
      // Arrange
      const id = 1;
      const updateDto: UpdateCustomFieldDto = {
        label: 'Họ và tên đầy đủ',
        configJson: {
          validation: { minLength: 5, maxLength: 60, pattern: '^[a-zA-Z0-9 ]*$' },
          placeholder: 'Nhập họ và tên đầy đủ',
          variant: 'outlined',
          size: 'small',
        },
      };

      const existingField = new CustomField();
      existingField.id = 1;
      existingField.component = 'Text Input';
      existingField.configId = 'text-input';
      existingField.label = 'Họ và tên';
      existingField.type = 'text';
      existingField.required = true;
      existingField.configJson = {
        validation: { minLength: 3, maxLength: 50, pattern: '^[a-zA-Z0-9 ]*$' },
        placeholder: 'Nhập họ và tên',
        variant: 'outlined',
        size: 'small',
      };
      existingField.userId = 1001;
      existingField.employeeId = 0;
      existingField.status = EntityStatusEnum.PENDING;
      existingField.createAt = 1741708800000;

      const updatedField = { ...existingField };
      updatedField.label = updateDto.label;
      updatedField.configJson = updateDto.configJson;

      mockCustomFieldRepository.findByIdAndNotDeleted.mockResolvedValue(existingField);
      mockCustomFieldRepository.save.mockResolvedValue(updatedField);

      // Act
      const result = await service.update(id, updateDto);

      // Assert
      expect(validationHelper.validateCustomFieldExists).toHaveBeenCalledWith(existingField, id);
      expect(validationHelper.validateUpdateCustomFieldData).toHaveBeenCalledWith(updateDto, existingField);
      expect(customFieldRepository.save).toHaveBeenCalled();
      expect(result.label).toEqual(updateDto.label);
      expect(result.configJson).toEqual(updateDto.configJson);
    });

    it('should throw an exception if custom field is not found', async () => {
      // Arrange
      const id = 999;
      const updateDto: UpdateCustomFieldDto = {
        label: 'Họ và tên đầy đủ',
      };

      mockCustomFieldRepository.findByIdAndNotDeleted.mockResolvedValue(null);
      mockValidationHelper.validateCustomFieldExists.mockImplementation(() => {
        throw new AppException(
          BUSINESS_ERROR_CODES.CUSTOM_FIELD_NOT_FOUND,
          `Không tìm thấy trường tùy chỉnh với ID ${id}`,
        );
      });

      // Act & Assert
      await expect(service.update(id, updateDto)).rejects.toThrow(AppException);
      expect(customFieldRepository.findByIdAndNotDeleted).toHaveBeenCalledWith(id);
      expect(validationHelper.validateCustomFieldExists).toHaveBeenCalledWith(null, id);
    });
  });

  describe('delete', () => {
    it('should delete a custom field successfully', async () => {
      // Arrange
      const id = 1;
      const userId = 1001;
      const existingField = new CustomField();
      existingField.id = 1;
      existingField.userId = userId;
      existingField.status = EntityStatusEnum.ACTIVE;

      const groupFormFields = [
        { fieldId: 1, formGroupId: 1 },
        { fieldId: 1, formGroupId: 2 },
      ] as CustomGroupFormField[];

      mockCustomFieldRepository.findByIdAndNotDeleted.mockResolvedValue(existingField);
      mockCustomGroupFormFieldRepository.find.mockResolvedValue(groupFormFields);
      mockCustomFieldRepository.save.mockResolvedValue({ ...existingField, status: EntityStatusEnum.DELETED });
      mockCustomGroupFormFieldRepository.remove.mockResolvedValue(undefined);

      // Act
      await service.delete(id, userId);

      // Assert
      expect(validationHelper.validateCustomFieldExists).toHaveBeenCalledWith(existingField, id);
      expect(existingField.status).toEqual(EntityStatusEnum.DELETED);
      expect(customFieldRepository.save).toHaveBeenCalledWith(existingField);
      expect(customGroupFormFieldRepository.find).toHaveBeenCalledWith({ where: { fieldId: id } });
      expect(customGroupFormFieldRepository.remove).toHaveBeenCalledWith(groupFormFields);
    });

    it('should throw an exception if custom field is not found', async () => {
      // Arrange
      const id = 999;
      const userId = 1001;

      mockCustomFieldRepository.findByIdAndNotDeleted.mockResolvedValue(null);
      mockValidationHelper.validateCustomFieldExists.mockImplementation(() => {
        throw new AppException(
          BUSINESS_ERROR_CODES.CUSTOM_FIELD_NOT_FOUND,
          `Không tìm thấy trường tùy chỉnh với ID ${id}`,
        );
      });

      // Act & Assert
      await expect(service.delete(id, userId)).rejects.toThrow(AppException);
      expect(customFieldRepository.findByIdAndNotDeleted).toHaveBeenCalledWith(id);
      expect(validationHelper.validateCustomFieldExists).toHaveBeenCalledWith(null, id);
    });
  });

  describe('getComponents', () => {
    it('should return default components and user components', async () => {
      // Arrange
      const userId = 1001;
      const customFields = [
        {
          id: 1,
          component: 'Text Input',
          configId: 'custom-text-001',
          label: 'Họ và tên',
          type: 'text',
          required: true,
          configJson: {
            validation: { minLength: 3, maxLength: 50, pattern: '^[a-zA-Z0-9 ]*$' },
            placeholder: 'Nhập họ và tên',
            variant: 'outlined',
            size: 'small',
          },
          userId: 1001,
          employeeId: 0,
          status: EntityStatusEnum.ACTIVE,
        },
      ] as CustomField[];

      mockCustomFieldRepository.find.mockResolvedValue(customFields);

      // Act
      const result = await service.getComponents(userId);

      // Assert
      expect(customFieldRepository.find).toHaveBeenCalledWith({
        where: { status: EntityStatusEnum.ACTIVE, userId },
      });
      expect(result.data.length).toEqual(DEFAULT_COMPONENTS.length + customFields.length);
      expect(result.total).toEqual(DEFAULT_COMPONENTS.length + customFields.length);

      // Verificar que los componentes personalizados se transformaron correctamente
      const customComponent = result.data.find(c => c.config.id === 'custom-text-001');
      expect(customComponent).toBeDefined();
      expect(customComponent.component).toEqual('Text Input');
      expect(customComponent.config.label).toEqual('Họ và tên');
    });

    it('should return only default components if no user components found', async () => {
      // Arrange
      const userId = 1001;
      mockCustomFieldRepository.find.mockResolvedValue([]);

      // Act
      const result = await service.getComponents(userId);

      // Assert
      expect(customFieldRepository.find).toHaveBeenCalledWith({
        where: { status: EntityStatusEnum.ACTIVE, userId },
      });
      expect(result.data.length).toEqual(DEFAULT_COMPONENTS.length);
      expect(result.total).toEqual(DEFAULT_COMPONENTS.length);
    });
  });
});
