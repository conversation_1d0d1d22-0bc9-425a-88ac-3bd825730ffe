import {
  registerDecorator,
  ValidationOptions,
  ValidationArguments,
} from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsString,
  IsNotEmpty,
  IsOptional,
  ValidateNested,
  IsBoolean,
  IsArray,
  IsUUID,
} from 'class-validator';
import { Type } from 'class-transformer';
import { TextContentBlockDto } from './text-content-block.dto';
import { ImageContentBlockDto } from './image-content-block.dto';
import { FileContentBlockDto } from './file-content-block.dto';
import { ToolCallDecisionDto } from './tool-call-decision.dto';
// ✅ REMOVED: ReplyToDto - replaced by flat replyToMessageId design
// ✅ REMOVED: AttachmentContext imports - now built from database

/**
 * ✅ UPDATED: DTO for content block types (ReplyToDto removed - use replyToMessageId field instead)
 */
type CreateContentDto =
  | TextContentBlockDto
  | ImageContentBlockDto
  | FileContentBlockDto
  | ToolCallDecisionDto;
export class MessageRequestDto {
  @ApiProperty({
    description: 'An array of content blocks that make up the message.',
    type: 'array',
    items: {
      oneOf: [
        { $ref: '#/components/schemas/TextContentBlockDto' },
        { $ref: '#/components/schemas/ImageContentBlockDto' },
        { $ref: '#/components/schemas/FileContentBlockDto' },
        { $ref: '#/components/schemas/ToolCallDecisionDto' },
        // ✅ REMOVED: ReplyToDto - use replyToMessageId field instead
      ],
    },
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => Object, {
    keepDiscriminatorProperty: true,
    discriminator: {
      property: 'type', // The field in the JSON that holds the type name
      subTypes: [
        { value: TextContentBlockDto, name: 'text' },
        { value: ImageContentBlockDto, name: 'image' },
        { value: FileContentBlockDto, name: 'file' },
        { value: ToolCallDecisionDto, name: 'tool_call_decision' },
      ],
    },
  })
   @OnlyOneToolCallDecisionBlockAllowed({
    message:
      'Only one "tool_call_decision" block is allowed, and it must be the only block.',
  })
  contentBlocks: CreateContentDto[];

  @ApiProperty({
    description: 'Required thread ID to continue existing conversation',
    example: 'deac627b-8be6-4d15-a050-f2074cdbbc55',
  })
  @IsNotEmpty()
  @IsString()
  threadId: string;

  @ApiPropertyOptional({
    description: 'Optional message ID to reply to',
    example: 'd74374dc-3e67-4bda-922e-2f11b3f835a8',
  })
  @IsOptional()
  @IsUUID()
  replyToMessageId?: string;

  @ApiPropertyOptional({
    description:
      'Whether to always approve tool calls without user confirmation',
    example: false,
  })
  @IsOptional()
  @IsBoolean()
  alwaysApproveToolCall?: boolean;


  

  @ApiProperty({
    description: 'optional message id used for editing'
  })
  @IsOptional()
  @IsUUID()
  messageId?: string;
}

export function OnlyOneToolCallDecisionBlockAllowed(validationOptions?: ValidationOptions) {
  return function (object: Object, propertyName: string) {
    registerDecorator({
      name: 'OnlyOneToolCallDecisionBlockAllowed',
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      validator: {
        validate(value: any[], args: ValidationArguments) {
          if (!Array.isArray(value)) return false;

          const toolCallBlocks = value.filter(
            (block) => block?.type === 'tool_call_decision',
          );

          if (toolCallBlocks.length > 1) return false;

          if (toolCallBlocks.length === 1 && value.length > 1) return false;

          return true;
        },
        defaultMessage(args: ValidationArguments) {
          return `If a "tool_call_decision" block exists, it must be the only block. Only one such block is allowed.`;
        },
      },
    });
  };
}



/**
 * This file contains the example payloads for the MessageRequestDto
 * used in Swagger (OpenAPI) documentation.
 *
 * By abstracting these examples here, we keep the controller code clean
 * and make the examples easier to manage and reuse.
 */

// --- Reusable Mock IDs for Clarity ---
const THREAD_ID = 'deac627b-8be6-4d15-a050-f2074cdbbc55';
const REPLY_TO_MESSAGE_ID = 'a1b2c3d4-e5f6-a7b8-c9d0-e1f2a3b4c5d6';
const EDIT_MESSAGE_ID = 'b2c3d4e5-f6a7-b8c9-d0e1-f2a3b4b5c6d7';

const FILE_ID_1 = 'f1a2b3c4-d5e6-f7a8-b9c0-d1e2f3a4b5c6';
const IMAGE_ID_1 = 'img1-a1b2-c3d4-d5e6-f7a8b9c0d1e2';
// ✅ REMOVED: FILE_PATH_1, IMAGE_PATH_1 - no longer needed without attachmentContext

// --- Individual Example Payloads ---

const simpleTextMessage: Partial<MessageRequestDto> = {
  threadId: THREAD_ID,
  contentBlocks: [{ type: 'text', content: 'Hello, how can I help you?' }],
};

const replyMessage: Partial<MessageRequestDto> = {
  threadId: THREAD_ID,
  replyToMessageId: REPLY_TO_MESSAGE_ID,
  contentBlocks: [{ type: 'text', content: 'Thanks for the information!' }],
};

const messageWithAttachments: Partial<MessageRequestDto> = {
  threadId: THREAD_ID,
  contentBlocks: [
    { type: 'text', content: 'Here are the files you requested:' },
    {
      type: 'image',
      fileId: IMAGE_ID_1,
      name: 'Screenshot.png',
      path: 'media/IMAGE/2025/06/user_1/screenshot.png', // Example path
      tags: ['screenshot', 'ui-mockup'],
    },
    {
      type: 'file',
      fileId: FILE_ID_1,
      name: 'Document.pdf',
      // 👇 MODIFIED
      tags: ['brief', 'q3-planning'],
    },
  ],
  // ✅ REMOVED: attachmentContext - now built from database via thread-media associations
};

const editLastMessage: Partial<MessageRequestDto> = {
  threadId: THREAD_ID,
  messageId: EDIT_MESSAGE_ID,
  contentBlocks: [
    { type: 'text', content: 'Sorry, I meant to say: The meeting is at 3 PM, not 2 PM.' },
    {
      type: 'file',
      fileId: FILE_ID_1,
      name: 'Meeting-Agenda.pdf',
      // 👇 MODIFIED
      tags: ['meeting', 'agenda', 'urgent'],
    },
  ],
  // ✅ REMOVED: attachmentContext - now built from database via thread-media associations
};

const approveToolCall: Partial<MessageRequestDto> = {
  threadId: THREAD_ID,
  contentBlocks: [
    {
      type: 'tool_call_decision',
      decision: 'yes',
      // 👇 MODIFIED
    },
  ],
};

const rejectToolCall: Partial<MessageRequestDto> = {
  threadId: THREAD_ID,
  contentBlocks: [
    {
      type: 'tool_call_decision',
      decision: 'no',
      // 👇 MODIFIED
    },
  ],
};


// --- Assembled Examples for Swagger Decorator ---

export const MessageRequestExamples = {
  'Simple Text Message': {
    summary: 'Basic text message',
    description: 'A simple message with just text content.',
    value: simpleTextMessage,
  },
  'Reply Message': {
    summary: 'Reply to a previous message',
    description: 'Uses the replyToMessageId field to indicate a reply.',
    value: replyMessage,
  },
  'Message with Attachments': {
    summary: 'Message with an image and a file',
    description: 'Contains text, an image, and a file. Note the new `path` and `tags` properties.',
    value: messageWithAttachments,
  },
  'Edit Last Message': {
    summary: 'Edit a previous message',
    description: 'Modifies a previously sent message by providing its messageId. Note the new `tags` property on the file.',
    value: editLastMessage,
  },
  'Approve Tool Call': {
    summary: 'Approve a pending tool call',
    description: 'Note the new `tags` property on the decision block.',
    value: approveToolCall,
  },
  'Reject Tool Call': {
    summary: 'Reject a pending tool call',
    description: 'Note the new `tags` property on the decision block.',
    value: rejectToolCall,
  },
};