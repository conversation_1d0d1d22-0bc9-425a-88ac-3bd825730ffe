# Changelog: Thêm trường owner_type vào Media Entity

## <PERSON><PERSON> tả thay đổi
Đã thêm trường `owner_type` vào Media entity để phân biệt media được tạo bởi USER hay ADMIN.

## Các thay đổi đã thực hiện

### 1. Tạo enum OwnerTypeEnum
- **File**: `src/modules/data/media/enums/owner-type.enum.ts`
- **Nội dung**: Enum với 2 giá trị 'USER' và 'ADMIN'

### 2. Cập nhật Media Entity
- **File**: `src/modules/data/media/entities/media.entity.ts`
- **Thay đổi**: 
  - Thêm import `OwnerTypeEnum`
  - Thêm trường `ownerType` với type `OwnerTypeEnum`, default là `USER`

### 3. Cập nhật MediaSchema
- **File**: `src/modules/data/media/schemas/media.schema.ts`
- **<PERSON><PERSON> đổi**: 
  - Thêm import `OwnerTypeEnum`
  - Thêm property `ownerType` với Swagger documentation

### 4. Cập nhật Admin Service
- **File**: `src/modules/data/media/admin/services/media-admin.service.ts`
- **Thay đổi**: 
  - Thêm import `OwnerTypeEnum`
  - Set `ownerType: OwnerTypeEnum.ADMIN` khi tạo media entity trong `createPresignedUrlsFromMediaList`

### 5. Cập nhật User Service
- **File**: `src/modules/data/media/user/services/media-user.service.ts`
- **Thay đổi**: 
  - Thêm import `OwnerTypeEnum`
  - Set `ownerType: OwnerTypeEnum.USER` khi tạo media entity trong `createPresignedUrlsFromMediaList`

### 6. Cập nhật Export Files
- **File**: `src/modules/data/media/enums/index.ts`
- **Thay đổi**: Thêm export cho `owner-type.enum`, `media-status.enum`

### 7. Cập nhật Test Files
- **File**: `src/modules/data/media/admin/test-presigned-urls.http`
- **Thay đổi**: Thêm test cases cho cả admin và user APIs với mô tả về owner_type

## Cách sử dụng

### API Admin Presigned URLs
```http
POST /admin/media/presigned-urls
```
- Media được tạo sẽ có `owner_type = 'ADMIN'`

### API User Presigned URLs  
```http
POST /media/presigned-urls
```
- Media được tạo sẽ có `owner_type = 'USER'`

## Database Schema
Trường `owner_type` đã được thêm vào bảng `media_data`:
- Type: ENUM('USER', 'ADMIN')
- Default: 'USER'
- Column name: `owner_type`

## Lưu ý
- Trường `owner_type` được set tự động trong service, không cần truyền từ client
- Admin API sẽ luôn tạo media với `owner_type = 'ADMIN'`
- User API sẽ luôn tạo media với `owner_type = 'USER'`
- Có thể sử dụng trường này để filter, phân quyền hoặc thống kê media theo loại chủ sở hữu
