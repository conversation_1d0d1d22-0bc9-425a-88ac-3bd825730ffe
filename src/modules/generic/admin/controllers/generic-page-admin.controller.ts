import { Body, Controller, Delete, Get, Param, Post, Put, Query, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiExtraModels, ApiOperation, ApiParam, ApiResponse, ApiTags } from '@nestjs/swagger';
import { JwtEmployeeGuard } from '@modules/auth/guards';
import { CurrentEmployee } from '@modules/auth/decorators/current-employee.decorator';
import { JWTPayload } from '@modules/auth/interfaces';
import { ApiResponseDto, PaginatedResult } from '@common/response';
import { GenericPageAdminService } from '../services';
import { CreateGenericPageDto, GenericPageResponseDto, QueryGenericPageDto, UpdateGenericPageDto } from '../dto';

@ApiTags('Admin Generic Page')
@Controller('admin/generic-pages')
@UseGuards(JwtEmployeeGuard)
@ApiBearerAuth('JWT-auth')
@ApiExtraModels(ApiResponseDto, GenericPageResponseDto, PaginatedResult)
export class GenericPageAdminController {
  constructor(private readonly genericPageAdminService: GenericPageAdminService) {}

  /**
   * Tạo trang mới
   */
  @Post()
  @ApiOperation({ summary: 'Tạo trang mới' })
  @ApiResponse({
    status: 201,
    description: 'Trang đã được tạo',
    schema: ApiResponseDto.getSchema(GenericPageResponseDto),
  })
  async createGenericPage(
    @Body() createGenericPageDto: CreateGenericPageDto,
    @CurrentEmployee() employee: JWTPayload,
  ): Promise<ApiResponseDto<GenericPageResponseDto>> {
    const result = await this.genericPageAdminService.createGenericPage(
      createGenericPageDto,
      String(employee.id),
    );
    return ApiResponseDto.created(result);
  }

  /**
   * Cập nhật trang
   */
  @Put(':id')
  @ApiOperation({ summary: 'Cập nhật trang' })
  @ApiParam({ name: 'id', description: 'ID của trang' })
  @ApiResponse({
    status: 200,
    description: 'Trang đã được cập nhật',
    schema: ApiResponseDto.getSchema(GenericPageResponseDto),
  })
  async updateGenericPage(
    @Param('id') id: string,
    @Body() updateGenericPageDto: UpdateGenericPageDto,
    @CurrentEmployee() employee: JWTPayload,
  ): Promise<ApiResponseDto<GenericPageResponseDto>> {
    const result = await this.genericPageAdminService.updateGenericPage(
      id,
      updateGenericPageDto,
      String(employee.id),
    );
    return ApiResponseDto.success(result);
  }

  /**
   * Lấy thông tin trang theo ID
   */
  @Get(':id')
  @ApiOperation({ summary: 'Lấy thông tin trang theo ID' })
  @ApiParam({ name: 'id', description: 'ID của trang' })
  @ApiResponse({
    status: 200,
    description: 'Thông tin trang',
    schema: ApiResponseDto.getSchema(GenericPageResponseDto),
  })
  async getGenericPageById(
    @Param('id') id: string,
  ): Promise<ApiResponseDto<GenericPageResponseDto>> {
    const result = await this.genericPageAdminService.getGenericPageById(id);
    return ApiResponseDto.success(result);
  }

  /**
   * Lấy thông tin trang theo đường dẫn
   */
  @Get('by-path/:path')
  @ApiOperation({ summary: 'Lấy thông tin trang theo đường dẫn' })
  @ApiParam({ name: 'path', description: 'Đường dẫn của trang' })
  @ApiResponse({
    status: 200,
    description: 'Thông tin trang',
    schema: ApiResponseDto.getSchema(GenericPageResponseDto),
  })
  async getGenericPageByPath(
    @Param('path') path: string,
  ): Promise<ApiResponseDto<GenericPageResponseDto>> {
    const result = await this.genericPageAdminService.getGenericPageByPath(path);
    return ApiResponseDto.success(result);
  }

  /**
   * Xuất bản trang
   */
  @Post(':id/publish')
  @ApiOperation({ summary: 'Xuất bản trang' })
  @ApiParam({ name: 'id', description: 'ID của trang' })
  @ApiResponse({
    status: 200,
    description: 'Trang đã được xuất bản',
    schema: ApiResponseDto.getSchema(GenericPageResponseDto),
  })
  async publishGenericPage(
    @Param('id') id: string,
    @CurrentEmployee() employee: JWTPayload,
  ): Promise<ApiResponseDto<GenericPageResponseDto>> {
    const result = await this.genericPageAdminService.publishGenericPage(id, String(employee.id));
    return ApiResponseDto.success(result);
  }

  /**
   * Hủy xuất bản trang
   */
  @Post(':id/unpublish')
  @ApiOperation({ summary: 'Hủy xuất bản trang' })
  @ApiParam({ name: 'id', description: 'ID của trang' })
  @ApiResponse({
    status: 200,
    description: 'Trang đã được hủy xuất bản',
    schema: ApiResponseDto.getSchema(GenericPageResponseDto),
  })
  async unpublishGenericPage(
    @Param('id') id: string,
    @CurrentEmployee() employee: JWTPayload,
  ): Promise<ApiResponseDto<GenericPageResponseDto>> {
    const result = await this.genericPageAdminService.unpublishGenericPage(id, String(employee.id));
    return ApiResponseDto.success(result);
  }

  /**
   * Xóa trang
   */
  @Delete(':id')
  @ApiOperation({ summary: 'Xóa trang' })
  @ApiParam({ name: 'id', description: 'ID của trang' })
  @ApiResponse({
    status: 200,
    description: 'Trang đã được xóa',
    schema: ApiResponseDto.getSchema(null),
  })
  async deleteGenericPage(
    @Param('id') id: string,
  ): Promise<ApiResponseDto<null>> {
    await this.genericPageAdminService.deleteGenericPage(id);
    return ApiResponseDto.success(null, 'Trang đã được xóa thành công');
  }
}
