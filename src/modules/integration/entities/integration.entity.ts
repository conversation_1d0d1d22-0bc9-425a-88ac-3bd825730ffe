import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';
import { OwnedTypeEnum, IntegrationTypeEnum } from '../enums';

/**
 * Entity đại diện cho bảng integration trong cơ sở dữ liệu
 * Quản lý thông tin các tích hợp của người dùng
 */
@Entity('integration')
export class Integration {
  /**
   * ID của tích hợp
   */
  @PrimaryGeneratedColumn({ name: 'id' })
  id: number;

  /**
   * Tên của tích hợp
   */
  @Column({
    name: 'integration_name',
    length: 255,
    nullable: false,
    comment: 'Tên của tích hợp'
  })
  integrationName: string;

  /**
   * Loại tích hợp
   */
  @Column({
    name: 'type',
    type: 'enum',
    enum: IntegrationTypeEnum,
    nullable: false,
    comment: 'Loại tích hợp'
  })
  type: IntegrationTypeEnum;

  /**
   * ID của người dùng
   */
  @Column({
    name: 'user_id',
    nullable: false,
    comment: 'ID của người dùng'
  })
  userId: number;

  /**
   * Thông tin bổ sung của tích hợp
   */
  @Column({
    name: 'info',
    type: 'json',
    nullable: true,
    comment: 'Thông tin bổ sung của tích hợp'
  })
  info: Record<string, any> | null;

  /**
   * Loại chủ sở hữu tích hợp
   */
  @Column({
    name: 'owned_type',
    type: 'enum',
    enum: OwnedTypeEnum,
    default: OwnedTypeEnum.USER,
    comment: 'Loại chủ sở hữu tích hợp'
  })
  ownedType: OwnedTypeEnum;

  /**
   * Thời điểm tạo tích hợp
   */
  @Column({
    name: 'created_at',
    type: 'timestamp',
    default: () => 'now()',
    nullable: false,
    comment: 'Thời điểm tạo tích hợp'
  })
  createdAt: Date;

  @Column({
    name: 'employee_id',
    nullable: true,
    comment: 'ID của nhân viên'
  })
  employeeId: number;
}