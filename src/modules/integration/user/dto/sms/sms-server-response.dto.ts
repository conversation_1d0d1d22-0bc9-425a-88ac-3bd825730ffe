import { ApiProperty } from '@nestjs/swagger';
import { OwnedTypeEnum } from '../../../enums';

/**
 * DTO cho response trả về thông tin cấu hình máy chủ SMS
 */
export class SmsServerResponseDto {
  @ApiProperty({
    description: 'ID của tích hợp SMS',
    example: 1,
  })
  id: number;

  @ApiProperty({
    description: 'Tên tích hợp do người dùng đặt',
    example: 'config FPT SMS',
  })
  integration_name: string;

  @ApiProperty({
    description: 'Loại tích hợp',
    example: 'SMS',
  })
  type: string;

  @ApiProperty({
    description: 'ID của người dùng sở hữu tích hợp',
    example: 123,
  })
  user_id: number;

  @ApiProperty({
    description: 'Thông tin cấu hình SMS (thông tin nhạy cảm được ẩn một phần vì bảo mật)',
    type: 'object',
    additionalProperties: true,
    example: {
      integrationName: 'FPT_SMS',
      apiKey: 'you***ere',
      endpoint: 'http://api.fpt.net/api',
      clientId: 'you***t-id'
    },
    nullable: true,
  })
  info: Record<string, any> | null;

  @ApiProperty({
    description: 'Loại chủ sở hữu tích hợp',
    enum: OwnedTypeEnum,
    example: OwnedTypeEnum.USER,
  })
  owned_type: OwnedTypeEnum;

  @ApiProperty({
    description: 'Thời gian tạo (ISO string)',
    example: '2023-01-01T00:00:00.000Z',
  })
  created_at: string;
}
