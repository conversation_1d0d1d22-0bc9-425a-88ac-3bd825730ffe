import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { AdminSegmentRepository } from '@modules/marketing/admin/repositories';
import { AdminAudienceRepository } from '@modules/marketing/admin/repositories';
import { CreateSegmentDto, UpdateSegmentDto, SegmentResponseDto, SegmentQueryDto, SegmentSortField } from '../dto/segment';
import { SortDirection } from '@/common/dto/query.dto';
import { PaginatedResponseDto, PaginationMetaDto } from '../dto/common';
import { AdminSegment } from '../entities';
import { Like, FindOptionsWhere } from 'typeorm';
import { Transactional } from 'typeorm-transactional';
import { BulkDeleteResponseDto } from '@/modules/marketing/common/dto';

/**
 * Service xử lý logic liên quan đến segment
 */
@Injectable()
export class AdminSegmentService {
  private readonly logger = new Logger(AdminSegmentService.name);

  constructor(
    private readonly adminSegmentRepository: AdminSegmentRepository,
    private readonly adminAudienceRepository: AdminAudienceRepository,
  ) {}

  /**
   * Tạo segment mới
   * @param createSegmentDto Dữ liệu tạo segment
   * @returns Segment đã tạo
   */
  @Transactional()
  async create(createSegmentDto: CreateSegmentDto): Promise<SegmentResponseDto> {
    this.logger.debug(`Tạo segment mới: ${JSON.stringify(createSegmentDto)}`);

    // Tạo segment mới
    const segment = new AdminSegment();
    segment.name = createSegmentDto.name;
    segment.description = createSegmentDto.description || '';
    segment.criteria = createSegmentDto.criteria;
    segment.createdAt = Math.floor(Date.now() / 1000);
    segment.updatedAt = Math.floor(Date.now() / 1000);

    // Lưu segment
    const savedSegment = await this.adminSegmentRepository.save(segment);

    // Đếm số lượng khách hàng trong segment
    const audienceCount = await this.countAudiencesInSegment(savedSegment);

    // Đảm bảo savedSegment là một đối tượng AdminSegment, không phải mảng
    return this.mapToDto(savedSegment as AdminSegment, audienceCount);
  }

  /**
   * Cập nhật segment
   * @param id ID của segment
   * @param updateSegmentDto Dữ liệu cập nhật segment
   * @returns Segment đã cập nhật
   */
  @Transactional()
  async update(id: number, updateSegmentDto: UpdateSegmentDto): Promise<SegmentResponseDto> {
    this.logger.debug(`Cập nhật segment với ID ${id}: ${JSON.stringify(updateSegmentDto)}`);

    // Kiểm tra segment tồn tại
    const segment = await this.adminSegmentRepository.findOne({
      where: { id },
    });

    if (!segment) {
      throw new NotFoundException(`Segment với ID ${id} không tồn tại`);
    }

    // Cập nhật thông tin segment
    if (updateSegmentDto.name !== undefined) {
      segment.name = updateSegmentDto.name;
    }

    if (updateSegmentDto.description !== undefined) {
      segment.description = updateSegmentDto.description;
    }

    if (updateSegmentDto.criteria !== undefined) {
      segment.criteria = updateSegmentDto.criteria;
    }

    segment.updatedAt = Math.floor(Date.now() / 1000);

    // Lưu segment
    const updatedSegment = await this.adminSegmentRepository.save(segment);

    // Đếm số lượng khách hàng trong segment
    const audienceCount = await this.countAudiencesInSegment(updatedSegment);

    // Đảm bảo updatedSegment là một đối tượng AdminSegment, không phải mảng
    return this.mapToDto(updatedSegment as AdminSegment, audienceCount);
  }

  /**
   * Lấy danh sách segment với phân trang và filter
   * @param query Tham số truy vấn
   * @returns Danh sách segment với phân trang
   */
  async findAll(query: SegmentQueryDto): Promise<PaginatedResponseDto<SegmentResponseDto>> {
    this.logger.debug(`Lấy danh sách segment với query: ${JSON.stringify(query)}`);

    const { page = 1, limit = 10, name, sortBy = SegmentSortField.CREATED_AT, sortDirection = SortDirection.DESC } = query;

    // Tính toán offset
    const offset = (page - 1) * limit;

    // Tạo điều kiện cơ bản
    const where: FindOptionsWhere<AdminSegment> = {};

    // Thêm điều kiện tìm kiếm theo tên
    if (name) {
      where.name = Like(`%${name}%`);
    }

    // Đếm tổng số segment
    const total = await this.adminSegmentRepository.count({ where });

    // Lấy danh sách segment với phân trang và sắp xếp
    const segments = await this.adminSegmentRepository.find({
      where,
      order: { [sortBy]: sortDirection },
      skip: offset,
      take: limit,
    });

    // Chuyển đổi kết quả thành DTO
    const data: SegmentResponseDto[] = [];

    for (const segment of segments) {
      // Đếm số lượng khách hàng trong segment
      const audienceCount = await this.countAudiencesInSegment(segment);

      data.push(this.mapToDto(segment, audienceCount));
    }

    // Tạo thông tin phân trang
    const totalPages = Math.ceil(total / limit);
    const meta: PaginationMetaDto = {
      total,
      page,
      limit,
      totalPages,
      hasPreviousPage: page > 1,
      hasNextPage: page < totalPages,
    };

    return {
      data,
      meta,
    };
  }

  /**
   * Lấy segment theo ID
   * @param id ID của segment
   * @returns Segment
   */
  async findOne(id: number): Promise<SegmentResponseDto> {
    this.logger.debug(`Lấy segment với ID ${id}`);

    // Kiểm tra segment tồn tại
    const segment = await this.adminSegmentRepository.findOne({
      where: { id },
    });

    if (!segment) {
      throw new NotFoundException(`Segment với ID ${id} không tồn tại`);
    }

    // Đếm số lượng khách hàng trong segment
    const audienceCount = await this.countAudiencesInSegment(segment);

    return this.mapToDto(segment, audienceCount);
  }

  /**
   * Xóa nhiều segment
   * @param ids Danh sách ID segment cần xóa
   * @returns Kết quả xóa nhiều
   */
  @Transactional()
  async bulkDelete(ids: number[]): Promise<BulkDeleteResponseDto> {
    this.logger.debug(`Xóa nhiều segment với IDs: ${ids.join(', ')}`);

    const deletedIds: number[] = [];
    const failedIds: number[] = [];

    for (const id of ids) {
      try {
        const segment = await this.adminSegmentRepository.findOne({ where: { id } });
        if (!segment) {
          failedIds.push(id);
          continue;
        }

        // Xóa segment
        await this.adminSegmentRepository.remove(segment);
        deletedIds.push(id);
      } catch (error) {
        this.logger.error(`Error deleting segment ${id}: ${error.message}`, error.stack);
        failedIds.push(id);
      }
    }

    const deletedCount = deletedIds.length;
    const failedCount = failedIds.length;
    const message = failedCount > 0
      ? `Đã xóa ${deletedCount} segment thành công, ${failedCount} segment không thể xóa`
      : `Đã xóa ${deletedCount} segment thành công`;

    return {
      deletedCount,
      failedCount,
      deletedIds,
      failedIds,
      message,
    };
  }

  /**
   * Xóa segment
   * @param id ID của segment
   * @returns true nếu xóa thành công
   */
  @Transactional()
  async remove(id: number): Promise<boolean> {
    this.logger.debug(`Xóa segment với ID ${id}`);

    // Kiểm tra segment tồn tại
    const segment = await this.adminSegmentRepository.findOne({
      where: { id },
    });

    if (!segment) {
      throw new NotFoundException(`Segment với ID ${id} không tồn tại`);
    }

    // Xóa segment
    await this.adminSegmentRepository.remove(segment);

    return true;
  }

  /**
   * Đếm số lượng khách hàng trong segment
   * @param segment Segment cần đếm
   * @returns Số lượng khách hàng
   */
  private async countAudiencesInSegment(segment: AdminSegment): Promise<number> {
    // TODO: Implement logic to count audiences based on segment criteria
    // This is a placeholder implementation
    // In a real implementation, you would use the criteria to filter audiences

    try {
      // Giả lập đếm số lượng khách hàng dựa trên tiêu chí
      // Trong thực tế, bạn sẽ cần xây dựng truy vấn dựa trên segment.criteria
      return Math.floor(Math.random() * 1000); // Giả lập số lượng khách hàng
    } catch (error) {
      this.logger.error(`Lỗi khi đếm số lượng khách hàng trong segment: ${error.message}`);
      return 0;
    }
  }

  /**
   * Chuyển đổi từ entity sang DTO
   * @param segment Segment entity
   * @param audienceCount Số lượng khách hàng trong segment
   * @returns Segment DTO
   */
  private mapToDto(segment: AdminSegment, audienceCount: number): SegmentResponseDto {
    const dto = new SegmentResponseDto();
    dto.id = segment.id;
    dto.name = segment.name;
    dto.description = segment.description;
    dto.criteria = segment.criteria;
    dto.createdAt = segment.createdAt;
    dto.updatedAt = segment.updatedAt;
    dto.audienceCount = audienceCount;
    return dto;
  }
}
