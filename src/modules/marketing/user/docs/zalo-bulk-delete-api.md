# API Xóa Nhiều Official Accounts

## Tổng quan

API này cho phép xóa nhiều Official Accounts cùng một lúc, gi<PERSON><PERSON> tiết kiệm thời gian khi cần xóa hàng loạt tài kho<PERSON>.

## Endpoint

```
DELETE /api/marketing/zalo/bulk-delete
```

## Authentication

- **Required**: JWT Bearer <PERSON>
- **Guard**: JwtUserGuard

## Request Body

### BulkDeleteOfficialAccountsDto

```typescript
{
  "oaIds": string[]  // Danh sách ID của các Official Account cần xóa
}
```

#### Validation Rules

- `oaIds`: 
  - Phải là một mảng
  - Tối thiểu 1 phần tử
  - Tối đa 50 phần tử
  - Mỗi phần tử phải là string không rỗng

#### Ví dụ Request

```json
{
  "oaIds": [
    "oa_123456789",
    "oa_987654321",
    "oa_111222333"
  ]
}
```

## Response

### BulkDeleteOfficialAccountsResponseDto

```typescript
{
  "success": boolean,
  "message": string,
  "data": {
    "totalRequested": number,      // Tổng số OA được yêu cầu xóa
    "successCount": number,        // Số OA xóa thành công
    "failureCount": number,        // Số OA xóa thất bại
    "successfulDeletes": string[], // Danh sách OA ID xóa thành công
    "failedDeletes": Array<{       // Danh sách OA ID xóa thất bại với lý do
      "oaId": string,
      "reason": string
    }>,
    "message": string              // Thông báo tổng kết
  }
}
```

## Ví dụ Response

### Thành công hoàn toàn

```json
{
  "success": true,
  "message": "Đã xóa thành công tất cả 3 Official Account",
  "data": {
    "totalRequested": 3,
    "successCount": 3,
    "failureCount": 0,
    "successfulDeletes": [
      "oa_123456789",
      "oa_987654321", 
      "oa_111222333"
    ],
    "failedDeletes": [],
    "message": "Đã xóa thành công tất cả 3 Official Account"
  }
}
```

### Thành công một phần

```json
{
  "success": true,
  "message": "Đã xóa thành công 2/3 Official Account",
  "data": {
    "totalRequested": 3,
    "successCount": 2,
    "failureCount": 1,
    "successfulDeletes": [
      "oa_123456789",
      "oa_987654321"
    ],
    "failedDeletes": [
      {
        "oaId": "oa_111222333",
        "reason": "Official Account đang được sử dụng trong chiến dịch, không thể xóa"
      }
    ],
    "message": "Đã xóa thành công 2/3 Official Account"
  }
}
```

## Các trường hợp lỗi

### 1. Validation Error (400)

```json
{
  "success": false,
  "message": "Dữ liệu đầu vào không hợp lệ",
  "errors": [
    {
      "field": "oaIds",
      "message": "Phải có ít nhất 1 Official Account để xóa"
    }
  ]
}
```

### 2. Unauthorized (401)

```json
{
  "success": false,
  "message": "Unauthorized",
  "statusCode": 401
}
```

### 3. Internal Server Error (500)

```json
{
  "success": false,
  "message": "Không thể xóa nhiều Official Account",
  "statusCode": 500
}
```

## Logic xử lý

### 1. Kiểm tra quyền truy cập
- Mỗi Official Account phải thuộc về user hiện tại
- Nếu không có quyền truy cập → thêm vào `failedDeletes`

### 2. Kiểm tra ràng buộc nghiệp vụ
- Official Account đang được sử dụng trong chiến dịch → không thể xóa
- Official Account đang được sử dụng trong tự động hóa → không thể xóa

### 3. Xóa dữ liệu liên quan
- Xóa segments liên quan
- Các dữ liệu khác có thể được xóa bằng cascade delete

### 4. Xóa Official Account
- Xóa record chính khỏi database
- Ghi log kết quả

## Lưu ý quan trọng

### Performance
- API xử lý tuần tự từng Official Account
- Với số lượng lớn (gần 50), thời gian xử lý có thể lâu
- Recommend: Xử lý batch nhỏ (5-10 OA mỗi lần)

### Transaction Safety
- Mỗi Official Account được xử lý trong transaction riêng
- Lỗi ở một OA không ảnh hưởng đến các OA khác

### Logging
- Ghi log chi tiết cho từng bước xử lý
- Dễ dàng debug khi có lỗi

## Ví dụ sử dụng

### JavaScript/TypeScript

```typescript
const bulkDeleteOfficialAccounts = async (oaIds: string[]) => {
  try {
    const response = await fetch('/api/marketing/zalo/bulk-delete', {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${accessToken}`
      },
      body: JSON.stringify({ oaIds })
    });

    const result = await response.json();
    
    if (result.success) {
      console.log(`Đã xóa thành công ${result.data.successCount}/${result.data.totalRequested} Official Account`);
      
      if (result.data.failedDeletes.length > 0) {
        console.log('Các OA xóa thất bại:');
        result.data.failedDeletes.forEach(failed => {
          console.log(`- ${failed.oaId}: ${failed.reason}`);
        });
      }
    } else {
      console.error('Lỗi:', result.message);
    }
  } catch (error) {
    console.error('Network error:', error);
  }
};

// Sử dụng
bulkDeleteOfficialAccounts(['oa_123456789', 'oa_987654321']);
```

### cURL

```bash
curl -X DELETE \
  'https://api.redai.com/api/marketing/zalo/bulk-delete' \
  -H 'Authorization: Bearer YOUR_JWT_TOKEN' \
  -H 'Content-Type: application/json' \
  -d '{
    "oaIds": [
      "oa_123456789",
      "oa_987654321",
      "oa_111222333"
    ]
  }'
```

## Best Practices

1. **Kiểm tra trước khi xóa**: Gọi API lấy danh sách OA để confirm trước khi xóa
2. **Xử lý batch nhỏ**: Không xóa quá nhiều OA cùng lúc
3. **Xử lý kết quả**: Luôn kiểm tra `failedDeletes` để xử lý các trường hợp lỗi
4. **Backup**: Cân nhắc backup dữ liệu quan trọng trước khi xóa
5. **User confirmation**: Hiển thị dialog xác nhận trước khi thực hiện xóa

## Security Considerations

- API chỉ cho phép xóa OA thuộc về user hiện tại
- Không thể xóa OA của user khác
- Rate limiting có thể được áp dụng để tránh abuse
- Audit log được ghi lại cho mục đích kiểm tra
