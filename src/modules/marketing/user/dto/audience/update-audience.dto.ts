import { IsEmail, IsOptional, IsString, ValidateNested, IsPhoneNumber } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { CreateCustomFieldDto } from './create-custom-field.dto';

/**
 * DTO cho việc cập nhật audience
 */
export class UpdateAudienceDto {
  /**
   * Tên của khách hàng
   * @example "Nguyễn Văn A"
   */
  @ApiProperty({
    description: 'Tên của khách hàng',
    example: 'Nguyễn Văn A',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Tên phải là chuỗi' })
  name?: string;

  /**
   * Email của khách hàng
   * @example "<EMAIL>"
   */
  @ApiProperty({
    description: 'Email của khách hàng',
    example: '<EMAIL>',
    required: false,
  })
  @IsOptional()
  @IsEmail({}, { message: '<PERSON><PERSON> không hợp lệ' })
  email?: string;

  /**
   * <PERSON>ố điện thoại của khách hàng
   * @example "+84912345678"
   */
  @ApiProperty({
    description: 'Số điện thoại của khách hàng',
    example: '+84912345678',
    required: false,
  })
  @IsOptional()
  @IsPhoneNumber(undefined, { message: 'Số điện thoại không hợp lệ' })
  phone?: string;

  /**
   * URL avatar của khách hàng (S3 key)
   * @example "customer_avatars/2024/01/1234567890-uuid.jpg"
   */
  @ApiProperty({
    description: 'URL avatar của khách hàng (S3 key)',
    example: 'customer_avatars/2024/01/1234567890-uuid.jpg',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Avatar phải là chuỗi' })
  avatar?: string;

  /**
   * Danh sách các trường tùy chỉnh
   */
  @ApiProperty({
    description: 'Danh sách các trường tùy chỉnh',
    type: [CreateCustomFieldDto],
    required: false,
  })
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => CreateCustomFieldDto)
  customFields?: CreateCustomFieldDto[];

  /**
   * Danh sách ID của các tag
   * @example [1, 2, 3]
   */
  @ApiProperty({
    description: 'Danh sách ID của các tag',
    example: [1, 2, 3],
    required: false,
    type: [Number],
  })
  @IsOptional()
  tagIds?: number[];
}
