### Test Zalo OAuth API

# Biến môi trường
@baseUrl = http://localhost:3000
@accessToken = your_jwt_token_here

### 1. Lấy URL OAuth Zalo
GET {{baseUrl}}/integration/zalo/oauth-url
Authorization: Bearer {{accessToken}}

### 2. Lấy URL OAuth Zalo với custom redirect URI
GET {{baseUrl}}/integration/zalo/oauth-url?redirectUri=https://v2.redai.vn/integration/zalo/oa
Authorization: Bearer {{accessToken}}

### 3. Test callback OAuth (GET) - Thành công
# URL này sẽ được Zalo gọi sau khi user cấp quyền
# State sẽ có format: zalo_integration_oa_1640995200000_abc123xyz
GET {{baseUrl}}/integration/zalo/oa?code=auth_code_123456&state=zalo_integration_oa_1640995200000_abc123xyz&user_id=123

### 4. Test callback OAuth (GET) - Lỗi từ Zalo
GET {{baseUrl}}/integration/zalo/oa?error=access_denied&error_description=User%20denied%20the%20request&state=zalo_integration_oa_1640995200000_abc123xyz

### 5. Test callback OAuth API (POST) - Thành công
POST {{baseUrl}}/integration/zalo/oa/callback
Authorization: Bearer {{accessToken}}
Content-Type: application/json

{
  "code": "auth_code_123456",
  "state": "zalo_integration_oa_1640995200000_abc123xyz"
}

### 6. Test callback OAuth API (POST) - Thiếu code
POST {{baseUrl}}/integration/zalo/oa/callback
Authorization: Bearer {{accessToken}}
Content-Type: application/json

{
  "state": "zalo_integration_oa_1640995200000_abc123xyz"
}

### 7. Test với production URL
# Đây là URL thực tế mà Zalo sẽ redirect về
# https://oauth.zaloapp.com/v4/oa/permission?app_id=3482178216594085616&redirect_uri=https%3A%2F%2Fv2.redai.vn%2Fintegration%2Fzalo%2Foa&state=randomstring&nocache=1234567890

### Luồng OAuth hoàn chỉnh:

# Bước 1: Frontend gọi API để lấy URL OAuth
# GET /integration/zalo/oauth-url
# Response: { "oauthUrl": "https://oauth.zaloapp.com/v4/oa/permission?...", "state": "..." }

# Bước 2: Frontend redirect user đến oauthUrl

# Bước 3: User đăng nhập và cấp quyền trên Zalo

# Bước 4: Zalo redirect về callback URL với authorization code
# GET /integration/zalo/oa?code=...&state=...&user_id=...

# Bước 5: Backend xử lý callback, lấy access token và lưu thông tin OA

# Bước 6: Backend redirect về frontend với kết quả
# https://v2.redai.vn/integration/zalo?success=true&oa_id=...&oa_name=...

### Cấu hình cần thiết trong .env:
# ZALO_APP_ID=3482178216594085616
# ZALO_APP_SECRET=your_app_secret
# ZALO_REDIRECT_URI=https://v2.redai.vn/integration/zalo/oa
# FRONTEND_URL=https://v2.redai.vn

### Cấu hình trong Zalo App:
# Redirect URI: https://v2.redai.vn/integration/zalo/oa
# Webhook URL: https://v2.redai.vn/webhook/zalo (nếu có)

### Test với curl:

# Lấy OAuth URL
# curl -X GET "http://localhost:3000/integration/zalo/oauth-url" \
#   -H "Authorization: Bearer your_jwt_token"

# Test callback
# curl -X POST "http://localhost:3000/integration/zalo/oa/callback" \
#   -H "Authorization: Bearer your_jwt_token" \
#   -H "Content-Type: application/json" \
#   -d '{"code":"auth_code_123456","state":"user123_randomstring"}'
