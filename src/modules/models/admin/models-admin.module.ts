import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule } from '@nestjs/config';
import { HttpModule } from '@nestjs/axios';
import { EmployeeModule } from '@modules/employee/employee.module';
import { ServicesModule } from '@shared/services/services.module';

// Import entities
import * as entities from '../entities';

// Import repositories
import * as repositories from '../repositories';
import { UserModelsRepository } from '../repositories/user-models.repository';
import { SystemModelKeyLlmRepository } from '../repositories/system-model-key-llm.repository';
import { UserModelKeyLlmRepository } from '../repositories/user-model-key-llm.repository';

// Import controllers
import {
  AdminModelRegistryController,
  AdminDataFineTuneController,
  AdminSystemKeyLlmController,
  AdminSystemModelsController,
} from './controllers';
import { AdminPerformanceController } from './controllers/admin-performance.controller';

// Import services
import {
  AdminModelRegistryService,
  AdminDataFineTuneService,
  AdminSystemKeyLlmService,
  AdminSystemModelsService,
} from './services';

// Import model discovery services
import {
  ModelDiscoveryService,
  SystemModelSyncService,
  UserModelSyncService,
  PerformanceMonitorService,
  PatternMatchingEngineService,
  BulkModelOperationsService,
} from '../services';

// Import helpers
import { ApiKeyEncryptionHelper } from '../helpers/api-key-encryption.helper';

/**
 * Module quản lý models cho admin
 */
@Module({
  imports: [
    TypeOrmModule.forFeature([
      entities.ModelRegistry,
      entities.SystemKeyLlm,
      entities.FineTuneHistories,
      entities.UserKeyLlm,
      entities.UserDataFineTune,
      entities.AdminDataFineTune,
      // New entities
      entities.UserModels,
      entities.UserModelKeyLlm,
      entities.UserModelFineTune,
      entities.SystemModels,
      entities.SystemModelKeyLlm,
    ]),
    ConfigModule,
    HttpModule,
    EmployeeModule,
    ServicesModule,
  ],
  controllers: [
    AdminModelRegistryController,
    AdminDataFineTuneController,
    AdminSystemKeyLlmController,
    AdminSystemModelsController,
    AdminPerformanceController,
  ],
  providers: [
    // Services
    AdminModelRegistryService,
    AdminDataFineTuneService,
    AdminSystemKeyLlmService,
    AdminSystemModelsService,

    // Model Discovery Services
    ModelDiscoveryService,
    SystemModelSyncService,
    UserModelSyncService,
    PerformanceMonitorService,
    PatternMatchingEngineService,
    BulkModelOperationsService,

    // Repositories
    repositories.ModelRegistryRepository,
    repositories.SystemKeyLlmRepository,
    repositories.FineTuneHistoriesRepository,
    repositories.UserKeyLlmRepository,
    repositories.UserDataFineTuneRepository,
    repositories.AdminDataFineTuneRepository,
    repositories.SystemModelsRepository,
    UserModelsRepository,
    SystemModelKeyLlmRepository,
    UserModelKeyLlmRepository,

    // Helpers
    ApiKeyEncryptionHelper,
  ],
  exports: [
    AdminModelRegistryService,
    AdminDataFineTuneService,
    AdminSystemKeyLlmService,
    AdminSystemModelsService,

    // Export model discovery services for use in other modules
    ModelDiscoveryService,
    SystemModelSyncService,
    UserModelSyncService,
  ],
})
export class ModelsAdminModule {}
