import { Injectable, Logger } from '@nestjs/common';
import { <PERSON><PERSON><PERSON>iderHelper } from '@/shared/services/ai/helpers/ai-provider.helper';
import { ModelRegistryRepository } from '../repositories/model-registry.repository';
import { SystemModelsRepository } from '../repositories/system-models.repository';
import { UserModelsRepository } from '../repositories/user-models.repository';
import { SystemModelKeyLlmRepository } from '../repositories/system-model-key-llm.repository';
import { UserModelKeyLlmRepository } from '../repositories/user-model-key-llm.repository';
import { PatternMatchingEngineService, CompiledPattern } from './pattern-matching-engine.service';
import { PerformanceMonitorService } from './performance-monitor.service';
import { BulkModelOperationsService, BulkModelData } from './bulk-model-operations.service';

/**
 * Interface cho model response từ AI providers
 */
export interface ModelResponse {
  id: string;
  name?: string;
  description?: string;
  [key: string]: any;
}

/**
 * Interface cho discovery result
 */
export interface OptimizedModelDiscoveryResult {
  totalModelsFound: number;
  modelsMatched: number;
  newModelsCreated: number;
  existingModelsFound: number;
  mappingsCreated: number;
  savedModels: Array<{
    modelId: string;
    modelRegistryId: string;
    isNew: boolean;
  }>;
  errors: string[];
  performanceMetrics?: {
    patternMatchingTime: number;
    databaseOperationTime: number;
    totalTime: number;
    cacheHitRate: number;
  };
}

/**
 * Optimized Model Discovery Service
 * Sử dụng PatternMatchingEngine để giảm complexity từ O(N×M×log M) xuống O(N×log M)
 */
@Injectable()
export class OptimizedModelDiscoveryService {
  private readonly logger = new Logger(OptimizedModelDiscoveryService.name);

  constructor(
    private readonly aiProviderHelper: AiProviderHelper,
    private readonly modelRegistryRepository: ModelRegistryRepository,
    private readonly systemModelsRepository: SystemModelsRepository,
    private readonly userModelsRepository: UserModelsRepository,
    private readonly systemModelKeyLlmRepository: SystemModelKeyLlmRepository,
    private readonly userModelKeyLlmRepository: UserModelKeyLlmRepository,
    private readonly patternMatchingEngine: PatternMatchingEngineService,
    private readonly performanceMonitor: PerformanceMonitorService,
    private readonly bulkModelOperations: BulkModelOperationsService,
  ) {}

  /**
   * Discover system models với optimized pattern matching
   * @param models Models từ AI provider
   * @param keyId System key ID
   * @param provider Provider name
   * @returns Discovery result
   */
  async discoverSystemModels(
    models: ModelResponse[],
    keyId: string,
    provider: string
  ): Promise<OptimizedModelDiscoveryResult> {
    const operationId = `discover_system_models_${keyId}_${Date.now()}`;
    
    this.performanceMonitor.startMonitoring(operationId, {
      operation: 'discoverSystemModels',
      keyId,
      provider,
      modelCount: models.length
    });

    try {
      // Step 1: Get và cache patterns
      const patterns = await this.getCompiledPatterns(provider);
      const patternMatchStart = Date.now();

      // Step 2: Extract model IDs
      const modelIds = models.map(model => this.getModelId(model));

      // Step 3: Optimized pattern matching - O(N×log M)
      const matches = this.patternMatchingEngine.matchModels(modelIds, patterns, provider);
      const patternMatchTime = Date.now() - patternMatchStart;

      // Step 4: Prepare matched models
      const matchedModels: Array<{ model: ModelResponse; registryId: string; score: number }> = [];
      for (const model of models) {
        const modelId = this.getModelId(model);
        const match = matches.get(modelId);

        if (match) {
          matchedModels.push({
            model,
            registryId: match.registryId,
            score: match.score
          });
        }
      }

      // Sort by match score (best matches first)
      matchedModels.sort((a, b) => b.score - a.score);

      this.logger.log(`Pattern matching completed: ${matchedModels.length}/${models.length} models matched in ${patternMatchTime}ms`);

      // Step 5: Bulk save operations
      const dbOperationStart = Date.now();
      const result = await this.bulkSaveSystemModels(matchedModels, keyId);
      const dbOperationTime = Date.now() - dbOperationStart;

      // Update performance metrics
      this.performanceMonitor.updateMetrics(operationId, {
        modelsProcessed: models.length,
        patternsMatched: matchedModels.length,
        newModelsCreated: result.newModelsCreated,
        mappingsCreated: result.mappingsCreated
      });

      const totalMetrics = this.performanceMonitor.stopMonitoring(operationId);

      // Add performance metrics to result
      result.performanceMetrics = {
        patternMatchingTime: patternMatchTime,
        databaseOperationTime: dbOperationTime,
        totalTime: totalMetrics?.duration || 0,
        cacheHitRate: this.calculateCacheHitRate(provider)
      };

      this.logger.log(`System model discovery completed: ${result.newModelsCreated} new, ${result.existingModelsFound} existing`);
      return result;

    } catch (error) {
      this.performanceMonitor.addError(operationId, error.message);
      this.logger.error(`System model discovery failed: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Discover user models với optimized pattern matching
   * @param models Models từ AI provider
   * @param keyId User key ID
   * @param provider Provider name
   * @returns Discovery result
   */
  async discoverUserModels(
    models: ModelResponse[],
    keyId: string,
    provider: string
  ): Promise<OptimizedModelDiscoveryResult> {
    const operationId = `discover_user_models_${keyId}_${Date.now()}`;
    
    this.performanceMonitor.startMonitoring(operationId, {
      operation: 'discoverUserModels',
      keyId,
      provider,
      modelCount: models.length
    });

    try {
      // Step 1: Get và cache patterns
      const patterns = await this.getCompiledPatterns(provider);
      const patternMatchStart = Date.now();

      // Step 2: Extract model IDs
      const modelIds = models.map(model => this.getModelId(model));

      // Step 3: Optimized pattern matching - O(N×log M)
      const matches = this.patternMatchingEngine.matchModels(modelIds, patterns, provider);
      const patternMatchTime = Date.now() - patternMatchStart;

      // Step 4: Prepare matched models
      const matchedModels: Array<{ model: ModelResponse; registryId: string; score: number }> = [];
      for (const model of models) {
        const modelId = this.getModelId(model);
        const match = matches.get(modelId);

        if (match) {
          matchedModels.push({
            model,
            registryId: match.registryId,
            score: match.score
          });
        }
      }

      // Sort by match score (best matches first)
      matchedModels.sort((a, b) => b.score - a.score);

      this.logger.log(`Pattern matching completed: ${matchedModels.length}/${models.length} models matched in ${patternMatchTime}ms`);

      // Step 5: Bulk save operations
      const dbOperationStart = Date.now();
      const result = await this.bulkSaveUserModels(matchedModels, keyId);
      const dbOperationTime = Date.now() - dbOperationStart;

      // Update performance metrics
      this.performanceMonitor.updateMetrics(operationId, {
        modelsProcessed: models.length,
        patternsMatched: matchedModels.length,
        newModelsCreated: result.newModelsCreated,
        mappingsCreated: result.mappingsCreated
      });

      const totalMetrics = this.performanceMonitor.stopMonitoring(operationId);

      // Add performance metrics to result
      result.performanceMetrics = {
        patternMatchingTime: patternMatchTime,
        databaseOperationTime: dbOperationTime,
        totalTime: totalMetrics?.duration || 0,
        cacheHitRate: this.calculateCacheHitRate(provider)
      };

      this.logger.log(`User model discovery completed: ${result.newModelsCreated} new, ${result.existingModelsFound} existing`);
      return result;

    } catch (error) {
      this.performanceMonitor.addError(operationId, error.message);
      this.logger.error(`User model discovery failed: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get compiled patterns với caching
   * @param provider Provider name
   * @returns Compiled patterns
   */
  private async getCompiledPatterns(provider: string): Promise<CompiledPattern[]> {
    const cacheKey = `patterns_${provider}`;
    
    // Try cache first
    let patterns = this.patternMatchingEngine.getCachedPatterns(cacheKey);
    
    if (!patterns) {
      // Cache miss - fetch from database
      this.logger.debug(`Cache miss for patterns: ${provider}`);
      
      const registryPatterns = await this.modelRegistryRepository.findActivePatterns();
      patterns = this.patternMatchingEngine.compilePatterns(registryPatterns, provider);
      
      // Cache for future use
      this.patternMatchingEngine.cachePatterns(cacheKey, patterns);
      
      this.logger.debug(`Compiled and cached ${patterns.length} patterns for provider: ${provider}`);
    } else {
      this.logger.debug(`Cache hit for patterns: ${provider} (${patterns.length} patterns)`);
    }

    return patterns;
  }

  /**
   * Extract model ID từ model response
   * @param model Model response
   * @returns Model ID
   */
  private getModelId(model: ModelResponse): string {
    return model.id || model.name || 'unknown';
  }

  /**
   * Calculate cache hit rate
   * @param provider Provider name
   * @returns Cache hit rate (0-1)
   */
  private calculateCacheHitRate(provider: string): number {
    const stats = this.patternMatchingEngine.getCacheStats();
    const providerEntry = stats.entries.find(entry => entry.key.includes(provider));
    
    // Simple heuristic - if cache entry exists and not expired, assume good hit rate
    if (providerEntry && !providerEntry.isExpired) {
      return 0.9; // 90% hit rate
    }
    
    return 0.1; // 10% hit rate (mostly cache misses)
  }

  /**
   * Bulk save system models - Optimized database operations
   * @param matchedModels Matched models với scores
   * @param keyId System key ID
   * @returns Save result
   */
  private async bulkSaveSystemModels(
    matchedModels: Array<{ model: ModelResponse; registryId: string; score: number }>,
    keyId: string
  ): Promise<OptimizedModelDiscoveryResult> {
    if (matchedModels.length === 0) {
      return {
        totalModelsFound: 0,
        modelsMatched: 0,
        newModelsCreated: 0,
        existingModelsFound: 0,
        mappingsCreated: 0,
        savedModels: [],
        errors: []
      };
    }

    try {
      // Prepare bulk model data
      const bulkModelData: BulkModelData[] = matchedModels.map(({ model, registryId, score }) => ({
        modelId: this.getModelId(model),
        modelRegistryId: registryId,
        metadata: {
          score,
          originalModel: model
        }
      }));

      // Execute bulk upsert operation - O(1) instead of O(N)
      const bulkResult = await this.bulkModelOperations.bulkUpsertSystemModels(
        bulkModelData,
        keyId
      );

      // Transform bulk result to discovery result
      const savedModels = matchedModels.map(({ model, registryId }) => ({
        modelId: this.getModelId(model),
        modelRegistryId: registryId,
        isNew: bulkResult.modelIdMap.has(this.getModelId(model))
      }));

      return {
        totalModelsFound: matchedModels.length,
        modelsMatched: matchedModels.length,
        newModelsCreated: bulkResult.newModelsCreated,
        existingModelsFound: bulkResult.existingModelsFound,
        mappingsCreated: bulkResult.mappingsCreated,
        savedModels,
        errors: bulkResult.errors
      };

    } catch (error) {
      this.logger.error(`Bulk save system models failed: ${error.message}`, error.stack);
      return {
        totalModelsFound: matchedModels.length,
        modelsMatched: matchedModels.length,
        newModelsCreated: 0,
        existingModelsFound: 0,
        mappingsCreated: 0,
        savedModels: [],
        errors: [`Bulk save failed: ${error.message}`]
      };
    }
  }

  /**
   * Bulk save user models - Optimized database operations
   * @param matchedModels Matched models với scores
   * @param keyId User key ID
   * @returns Save result
   */
  private async bulkSaveUserModels(
    matchedModels: Array<{ model: ModelResponse; registryId: string; score: number }>,
    keyId: string
  ): Promise<OptimizedModelDiscoveryResult> {
    if (matchedModels.length === 0) {
      return {
        totalModelsFound: 0,
        modelsMatched: 0,
        newModelsCreated: 0,
        existingModelsFound: 0,
        mappingsCreated: 0,
        savedModels: [],
        errors: []
      };
    }

    try {
      // Prepare bulk model data
      const bulkModelData: BulkModelData[] = matchedModels.map(({ model, registryId, score }) => ({
        modelId: this.getModelId(model),
        modelRegistryId: registryId,
        metadata: {
          score,
          originalModel: model
        }
      }));

      // Execute bulk upsert operation - O(1) instead of O(N)
      const bulkResult = await this.bulkModelOperations.bulkUpsertUserModels(
        bulkModelData,
        keyId
      );

      // Transform bulk result to discovery result
      const savedModels = matchedModels.map(({ model, registryId }) => ({
        modelId: this.getModelId(model),
        modelRegistryId: registryId,
        isNew: bulkResult.modelIdMap.has(this.getModelId(model))
      }));

      return {
        totalModelsFound: matchedModels.length,
        modelsMatched: matchedModels.length,
        newModelsCreated: bulkResult.newModelsCreated,
        existingModelsFound: bulkResult.existingModelsFound,
        mappingsCreated: bulkResult.mappingsCreated,
        savedModels,
        errors: bulkResult.errors
      };

    } catch (error) {
      this.logger.error(`Bulk save user models failed: ${error.message}`, error.stack);
      return {
        totalModelsFound: matchedModels.length,
        modelsMatched: matchedModels.length,
        newModelsCreated: 0,
        existingModelsFound: 0,
        mappingsCreated: 0,
        savedModels: [],
        errors: [`Bulk save failed: ${error.message}`]
      };
    }
  }
}
