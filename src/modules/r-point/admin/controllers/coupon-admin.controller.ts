import { Body, Controller, Delete, Get, Param, ParseUUIDPipe, Post, Put, Query, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiBody, ApiOperation, ApiParam, ApiQuery, ApiResponse, ApiTags } from '@nestjs/swagger';
import { JwtEmployeeGuard } from '@modules/auth/guards/jwt-employee.guard';
import { CouponAdminService } from '../services';
import { CreateCouponDto, UpdateCouponDto, CouponResponseDto, GetCouponsDto } from '../dto';
import { ApiResponseDto, PaginatedResult } from '@/common/response';
import { SWAGGER_API_TAGS } from '@/common/swagger';

@ApiTags(SWAGGER_API_TAGS.R_POINT_ADMIN_COUPONS)
@ApiBearerAuth("JWT-auth")
@UseGuards(JwtEmployeeGuard)
@Controller('admin/r-point/coupons')
export class CouponAdminController {
  constructor(private readonly couponAdminService: CouponAdminService) {}

  /**
   * Lấy danh sách coupon có phân trang
   * @param query Thông tin phân trang và tìm kiếm
   * @returns Danh sách coupon và thông tin phân trang
   */
  @Get()
  @ApiOperation({ summary: 'Lấy danh sách coupon có phân trang' })
  @ApiQuery({ type: GetCouponsDto })
  @ApiResponse({
    status: 200,
    description: 'Lấy danh sách coupon thành công',
    schema: ApiResponseDto.getPaginatedSchema(CouponResponseDto)
  })
  @ApiResponse({ status: 401, description: 'Không có quyền truy cập' })
  async findAll(@Query() query: GetCouponsDto): Promise<ApiResponseDto<PaginatedResult<CouponResponseDto>>> {
    const result = await this.couponAdminService.findAll(query);
    return ApiResponseDto.paginated(result, 'Lấy danh sách coupon thành công');
  }

  /**
   * Tạo mới coupon
   * @param createCouponDto Thông tin coupon cần tạo
   * @returns Thông tin coupon đã tạo
   */
  @Post()
  @ApiOperation({ summary: 'Tạo mới coupon' })
  @ApiBody({ type: CreateCouponDto })
  @ApiResponse({
    status: 201,
    description: 'Tạo mới coupon thành công',
    schema: {
      allOf: [
        {
          properties: {
            code: { type: 'number', example: 201 },
            message: { type: 'string', example: 'Tạo mới coupon thành công' },
            result: { $ref: '#/components/schemas/CouponResponseDto' }
          }
        }
      ]
    }
  })
  @ApiResponse({ status: 400, description: 'Dữ liệu không hợp lệ' })
  @ApiResponse({ status: 401, description: 'Không có quyền truy cập' })
  async create(@Body() createCouponDto: CreateCouponDto): Promise<ApiResponseDto<CouponResponseDto>> {
    const coupon = await this.couponAdminService.create(createCouponDto);
    return ApiResponseDto.success(coupon, 'Tạo mới coupon thành công');
  }

  /**
   * Cập nhật thông tin coupon
   * @param id ID của coupon cần cập nhật
   * @param updateCouponDto Thông tin cần cập nhật
   * @returns Thông tin coupon sau khi cập nhật
   */
  @Put(':id')
  @ApiOperation({ summary: 'Cập nhật thông tin coupon' })
  @ApiParam({ name: 'id', description: 'ID của coupon', type: 'string', format: 'uuid' })
  @ApiBody({ type: UpdateCouponDto })
  @ApiResponse({
    status: 200,
    description: 'Cập nhật coupon thành công',
    schema: {
      allOf: [
        {
          properties: {
            code: { type: 'number', example: 200 },
            message: { type: 'string', example: 'Cập nhật coupon thành công' },
            result: { $ref: '#/components/schemas/CouponResponseDto' }
          }
        }
      ]
    }
  })
  @ApiResponse({ status: 400, description: 'Dữ liệu không hợp lệ' })
  @ApiResponse({ status: 401, description: 'Không có quyền truy cập' })
  @ApiResponse({ status: 404, description: 'Không tìm thấy coupon' })
  async update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateCouponDto: UpdateCouponDto
  ): Promise<ApiResponseDto<CouponResponseDto>> {
    const coupon = await this.couponAdminService.update(id, updateCouponDto);
    return ApiResponseDto.success(coupon, 'Cập nhật coupon thành công');
  }

  /**
   * Xóa coupon
   * @param id ID của coupon cần xóa
   * @returns Thông tin xóa thành công
   */
  @Delete(':id')
  @ApiOperation({ summary: 'Xóa coupon' })
  @ApiParam({ name: 'id', description: 'ID của coupon', type: 'string', format: 'uuid' })
  @ApiResponse({
    status: 200,
    description: 'Xóa coupon thành công',
    schema: {
      allOf: [
        {
          properties: {
            code: { type: 'number', example: 200 },
            message: { type: 'string', example: 'Xóa coupon thành công' },
            result: {
              type: 'object',
              properties: {
                success: { type: 'boolean', example: true }
              }
            }
          }
        }
      ]
    }
  })
  @ApiResponse({ status: 401, description: 'Không có quyền truy cập' })
  @ApiResponse({ status: 404, description: 'Không tìm thấy coupon' })
  async remove(@Param('id', ParseUUIDPipe) id: string): Promise<ApiResponseDto<{ success: boolean }>> {
    const result = await this.couponAdminService.remove(id);
    return ApiResponseDto.success(result, 'Xóa coupon thành công');
  }

  /**
   * Xóa nhiều coupon
   * @param ids Danh sách ID của các coupon cần xóa
   * @returns Thông tin xóa thành công
   */
  @Delete('bulk')
  @ApiOperation({ summary: 'Xóa nhiều coupon' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        ids: {
          type: 'array',
          items: {
            type: 'string',
            format: 'uuid'
          },
          description: 'Danh sách ID của các coupon cần xóa'
        }
      }
    }
  })
  @ApiResponse({
    status: 200,
    description: 'Xóa các coupon thành công',
    schema: {
      allOf: [
        {
          properties: {
            code: { type: 'number', example: 200 },
            message: { type: 'string', example: 'Xóa các coupon thành công' },
            result: { type: 'boolean', example: true }
          }
        }
      ]
    }
  })
  @ApiResponse({ status: 400, description: 'Dữ liệu không hợp lệ' })
  @ApiResponse({ status: 401, description: 'Không có quyền truy cập' })
  async bulkDelete(@Body('ids') ids: string[]): Promise<ApiResponseDto<boolean>> {
    await this.couponAdminService.bulkDelete(ids);
    return ApiResponseDto.success(true, 'Xóa các coupon thành công');
  }
}
