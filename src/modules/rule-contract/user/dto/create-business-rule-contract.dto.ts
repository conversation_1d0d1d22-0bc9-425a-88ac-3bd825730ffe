import { ApiProperty } from '@nestjs/swagger';
import { IsEmail, IsNotEmpty, IsOptional, IsString, MaxLength, MinLength } from 'class-validator';

/**
 * DTO cho yêu cầu tạo hợp đồng nguyên tắc cho doanh nghiệp
 */
export class CreateBusinessRuleContractDto {
  /**
   * Tên doanh nghiệp
   */
  @ApiProperty({
    description: 'Tên doanh nghiệp',
    example: 'Công ty TNHH ABC',
    required: true,
  })
  @IsNotEmpty({ message: 'Tên doanh nghiệp không được để trống' })
  @IsString({ message: 'Tên doanh nghiệp phải là chuỗi' })
  @MinLength(2, { message: 'Tên doanh nghiệp phải có ít nhất 2 ký tự' })
  @MaxLength(255, { message: 'Tên doanh nghiệp không được vượt quá 255 ký tự' })
  businessName: string;

  /**
   * Tên người đại diện
   */
  @ApiProperty({
    description: 'Tên người đại diện',
    example: 'Nguyễn Văn A',
    required: true,
  })
  @IsNotEmpty({ message: 'Tên người đại diện không được để trống' })
  @IsString({ message: 'Tên người đại diện phải là chuỗi' })
  @MinLength(2, { message: 'Tên người đại diện phải có ít nhất 2 ký tự' })
  @MaxLength(100, { message: 'Tên người đại diện không được vượt quá 100 ký tự' })
  representativeName: string;

  /**
   * Chức vụ người đại diện
   */
  @ApiProperty({
    description: 'Chức vụ người đại diện',
    example: 'Giám đốc',
    required: true,
  })
  @IsNotEmpty({ message: 'Chức vụ người đại diện không được để trống' })
  @IsString({ message: 'Chức vụ người đại diện phải là chuỗi' })
  @MinLength(2, { message: 'Chức vụ người đại diện phải có ít nhất 2 ký tự' })
  @MaxLength(255, { message: 'Chức vụ người đại diện không được vượt quá 255 ký tự' })
  representativePosition: string;

  /**
   * Email doanh nghiệp
   */
  @ApiProperty({
    description: 'Email doanh nghiệp',
    example: '<EMAIL>',
    required: true,
  })
  @IsNotEmpty({ message: 'Email doanh nghiệp không được để trống' })
  @IsEmail({}, { message: 'Email doanh nghiệp không hợp lệ' })
  @MaxLength(100, { message: 'Email doanh nghiệp không được vượt quá 100 ký tự' })
  businessEmail: string;

  /**
   * Số điện thoại doanh nghiệp
   */
  @ApiProperty({
    description: 'Số điện thoại doanh nghiệp',
    example: '0912345678',
    required: true,
  })
  @IsNotEmpty({ message: 'Số điện thoại doanh nghiệp không được để trống' })
  @IsString({ message: 'Số điện thoại doanh nghiệp phải là chuỗi' })
  @MaxLength(20, { message: 'Số điện thoại doanh nghiệp không được vượt quá 20 ký tự' })
  businessPhone: string;

  /**
   * Địa chỉ doanh nghiệp
   */
  @ApiProperty({
    description: 'Địa chỉ doanh nghiệp',
    example: 'Số 123, Đường ABC, Quận XYZ, TP. Hồ Chí Minh',
    required: true,
  })
  @IsNotEmpty({ message: 'Địa chỉ doanh nghiệp không được để trống' })
  @IsString({ message: 'Địa chỉ doanh nghiệp phải là chuỗi' })
  @MinLength(5, { message: 'Địa chỉ doanh nghiệp phải có ít nhất 5 ký tự' })
  @MaxLength(1000, { message: 'Địa chỉ doanh nghiệp không được vượt quá 1000 ký tự' })
  businessAddress: string;

  /**
   * Mã số thuế
   */
  @ApiProperty({
    description: 'Mã số thuế',
    example: '1234567890',
    required: true,
  })
  @IsNotEmpty({ message: 'Mã số thuế không được để trống' })
  @IsString({ message: 'Mã số thuế phải là chuỗi' })
  @MaxLength(20, { message: 'Mã số thuế không được vượt quá 20 ký tự' })
  taxCode: string;
}
