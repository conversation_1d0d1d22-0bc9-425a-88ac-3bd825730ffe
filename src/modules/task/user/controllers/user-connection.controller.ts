import { JwtUserGuard } from '@/modules/auth/guards';
import { Controller, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { UserConnectionService } from '../services';
import { SWAGGER_API_TAGS } from '@/common/swagger';

/**
 * Controller xử lý các endpoint liên quan đến kết nối giữa các bước trong task của người dùng
 */
@ApiTags(SWAGGER_API_TAGS.USER_TASK)
@Controller('user/step-connections')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
export class UserConnectionController {
  /**
   * Constructor
   * @param userConnectionService Service xử lý logic liên quan đến kết nối giữa các bước trong task của người dùng
   */
  constructor(private readonly userConnectionService: UserConnectionService) {}
}
