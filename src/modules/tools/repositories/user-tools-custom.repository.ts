import { Injectable, Logger } from '@nestjs/common';
import { DataSource, Repository, SelectQueryBuilder } from 'typeorm';
import { Transactional } from 'typeorm-transactional';
import { UserToolsCustom } from '../entities/user-tools-custom.entity';
import { AppException } from '@common/exceptions';
import { CUSTOM_TOOLS_ERROR_CODES } from '../exceptions';
import { PaginatedResult } from '@common/response/api-response-dto';

@Injectable()
export class UserToolsCustomRepository extends Repository<UserToolsCustom> {
  private readonly logger = new Logger(UserToolsCustomRepository.name);

  constructor(private dataSource: DataSource) {
    super(UserToolsCustom, dataSource.createEntityManager());
  }

  /**
   * Tạo query builder cơ bản cho công cụ tùy chỉnh
   * @returns SelectQueryBuilder<UserToolsCustom> để sử dụng trong các phương thức khác
   */
  private createBaseQuery(): SelectQueryBuilder<UserToolsCustom> {
    return this.createQueryBuilder('customTool');
  }

  /**
   * Tìm công cụ tùy chỉnh theo ID
   * @param id ID của công cụ tùy chỉnh cần tìm
   * @returns Công cụ tùy chỉnh nếu tìm thấy, null nếu không tìm thấy
   */
  async findById(id: string): Promise<UserToolsCustom | null> {
    try {
      return this.createBaseQuery()
        .where('customTool.id = :id', { id })
        .getOne();
    } catch (error) {
      this.logger.error(`Lỗi khi tìm công cụ tùy chỉnh theo ID: ${error.message}`, error.stack);
      throw new AppException(CUSTOM_TOOLS_ERROR_CODES.CUSTOM_TOOL_NOT_FOUND, `Không tìm thấy công cụ tùy chỉnh với ID ${id}`);
    }
  }

  /**
   * Tìm công cụ tùy chỉnh theo ID người dùng và các tham số truy vấn
   * @param params Các tham số truy vấn
   * @returns Danh sách công cụ tùy chỉnh phân trang
   */
  async findByUserId(params: {
    userId: number;
    page?: number;
    limit?: number;
    search?: string;
    method?: string;
    sortBy?: string;
    sortDirection?: 'ASC' | 'DESC';
  }): Promise<PaginatedResult<UserToolsCustom>> {
    try {
      const {
        userId,
        page = 1,
        limit = 10,
        search,
        method,
        sortBy = 'createdAt',
        sortDirection = 'DESC',
      } = params;

      const skip = (page - 1) * limit;
      const qb = this.createBaseQuery().where('customTool.userId = :userId', { userId });

      // Thêm điều kiện tìm kiếm nếu có
      if (search) {
        qb.andWhere(
          '(customTool.toolName ILIKE :search OR customTool.toolDescription ILIKE :search OR customTool.baseUrl ILIKE :search)',
          { search: `%${search}%` }
        );
      }

      // Thêm điều kiện lọc theo phương thức HTTP nếu có
      if (method) {
        qb.andWhere('customTool.method = :method', { method });
      }

      // Xác định trường sắp xếp và hướng sắp xếp
      const orderByField = `customTool.${sortBy}`;

      // Thực hiện truy vấn với phân trang
      const [items, total] = await qb
        .orderBy(orderByField, sortDirection)
        .skip(skip)
        .take(limit)
        .getManyAndCount();

      // Tạo đối tượng meta cho phân trang
      const meta = {
        totalItems: total,
        itemCount: items.length,
        itemsPerPage: limit,
        totalPages: Math.ceil(total / limit),
        currentPage: page
      };

      return { items, meta };
    } catch (error) {
      this.logger.error(`Lỗi khi tìm công cụ tùy chỉnh theo ID người dùng: ${error.message}`, error.stack);
      throw new AppException(CUSTOM_TOOLS_ERROR_CODES.CUSTOM_TOOL_NOT_FOUND, 'Không tìm thấy công cụ tùy chỉnh');
    }
  }

  /**
   * Tạo mới công cụ tùy chỉnh
   * @param customTool Dữ liệu công cụ tùy chỉnh cần tạo
   * @returns Công cụ tùy chỉnh đã tạo
   */
  @Transactional()
  async createCustomTool(customTool: Partial<UserToolsCustom>): Promise<UserToolsCustom> {
    try {
      // Đảm bảo các trường JSONB có giá trị mặc định nếu không được cung cấp
      const queryParam = customTool.queryParam && Object.keys(customTool.queryParam).length > 0
        ? customTool.queryParam
        : {};

      const pathParam = customTool.pathParam && Object.keys(customTool.pathParam).length > 0
        ? customTool.pathParam
        : {};

      const body = customTool.body && Object.keys(customTool.body).length > 0
        ? customTool.body
        : {};

      // Loại bỏ các trường JSONB từ customTool
      const { queryParam: _, pathParam: __, body: ___, ...otherFields } = customTool;

      // Sử dụng QueryBuilder để kiểm soát chính xác các trường được chèn
      const result = await this.createQueryBuilder()
        .insert()
        .into(UserToolsCustom)
        .values({
          ...otherFields,
          createdAt: () => '(EXTRACT(epoch FROM now()) * 1000)::bigint',
          updatedAt: () => '(EXTRACT(epoch FROM now()) * 1000)::bigint'
        })
        .returning('*')
        .execute();

      // Lấy ID của bản ghi vừa chèn
      const insertedId = result.raw[0].id;

      // Cập nhật các trường JSONB riêng biệt
      await this.createQueryBuilder()
        .update(UserToolsCustom)
        .set({
          queryParam: () => ':queryParam',
          pathParam: () => ':pathParam',
          body: () => ':body'
        })
        .where('id = :id', { id: insertedId })
        .setParameters({
          queryParam: JSON.stringify(queryParam),
          pathParam: JSON.stringify(pathParam),
          body: JSON.stringify(body)
        })
        .execute();

      // Lấy bản ghi đã cập nhật
      const updatedTool = await this.findOne({ where: { id: insertedId } });

      if (!updatedTool) {
        throw new Error('Không thể tìm thấy công cụ vừa tạo');
      }

      return updatedTool;
    } catch (error) {
      this.logger.error(`Lỗi khi tạo công cụ tùy chỉnh: ${error.message}`, error.stack);
      throw new AppException(CUSTOM_TOOLS_ERROR_CODES.CUSTOM_TOOL_CREATE_FAILED, 'Tạo công cụ tùy chỉnh thất bại');
    }
  }

  /**
   * Cập nhật công cụ tùy chỉnh
   * @param id ID của công cụ tùy chỉnh cần cập nhật
   * @param customTool Dữ liệu cập nhật
   * @returns Void
   */
  @Transactional()
  async updateCustomTool(id: string, customTool: Partial<UserToolsCustom>): Promise<void> {
    try {
      // Tạo đối tượng cập nhật mới, loại bỏ các trường JSONB
      const {
        queryParam,
        pathParam,
        body,
        ...updateData
      } = customTool;

      // Tạo đối tượng cập nhật với các trường cơ bản
      const updateObj: any = {
        ...updateData,
        updatedAt: () => '(EXTRACT(epoch FROM now()) * 1000)::bigint',
      };

      // Thêm các trường JSONB nếu có
      if (queryParam !== undefined) {
        updateObj.queryParam = () => `:queryParam`;
      }

      if (pathParam !== undefined) {
        updateObj.pathParam = () => `:pathParam`;
      }

      if (body !== undefined) {
        updateObj.body = () => `:body`;
      }

      // Tạo đối tượng tham số
      const params: any = { id };

      if (queryParam !== undefined) {
        params.queryParam = JSON.stringify(queryParam);
      }

      if (pathParam !== undefined) {
        params.pathParam = JSON.stringify(pathParam);
      }

      if (body !== undefined) {
        params.body = JSON.stringify(body);
      }

      // Thực hiện cập nhật
      const result = await this.createQueryBuilder()
        .update(UserToolsCustom)
        .set(updateObj)
        .where('id = :id', { id })
        .setParameters(params)
        .execute();

      if (result.affected === 0) {
        throw new AppException(CUSTOM_TOOLS_ERROR_CODES.CUSTOM_TOOL_NOT_FOUND, `Không tìm thấy công cụ tùy chỉnh với ID ${id}`);
      }
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Lỗi khi cập nhật công cụ tùy chỉnh: ${error.message}`, error.stack);
      throw new AppException(CUSTOM_TOOLS_ERROR_CODES.CUSTOM_TOOL_UPDATE_FAILED, 'Cập nhật công cụ tùy chỉnh thất bại');
    }
  }

  /**
   * Xóa công cụ tùy chỉnh
   * @param id ID của công cụ tùy chỉnh cần xóa
   * @returns Void
   */
  @Transactional()
  async deleteCustomTool(id: string): Promise<void> {
    try {
      const result = await this.createQueryBuilder()
        .delete()
        .from(UserToolsCustom)
        .where('id = :id', { id })
        .execute();

      if (result.affected === 0) {
        throw new AppException(CUSTOM_TOOLS_ERROR_CODES.CUSTOM_TOOL_NOT_FOUND, `Không tìm thấy công cụ tùy chỉnh với ID ${id}`);
      }
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Lỗi khi xóa công cụ tùy chỉnh: ${error.message}`, error.stack);
      throw new AppException(CUSTOM_TOOLS_ERROR_CODES.CUSTOM_TOOL_DELETE_FAILED, 'Xóa công cụ tùy chỉnh thất bại');
    }
  }

  /**
   * Cập nhật trạng thái active của công cụ tùy chỉnh với một truy vấn duy nhất
   * @param id ID của công cụ tùy chỉnh
   * @param userId ID của người dùng
   * @returns Công cụ tùy chỉnh đã cập nhật hoặc null nếu không tìm thấy
   */
  @Transactional()
  async toggleActiveWithSingleQuery(id: string, userId: number): Promise<UserToolsCustom | null> {
    try {
      // Sử dụng CTE (Common Table Expression) để lấy thông tin hiện tại và cập nhật trong một truy vấn
      const result = await this.query(`
        WITH current_tool AS (
          SELECT id, active
          FROM user_tools_custom
          WHERE id = $1 AND user_id = $2
        ),
        updated_tool AS (
          UPDATE user_tools_custom
          SET active = NOT active,
              updated_at = (EXTRACT(epoch FROM now()) * 1000)::bigint
          WHERE id = $1 AND user_id = $2
          RETURNING id, tool_name, tool_description, created_at, updated_at, active
        )
        SELECT * FROM updated_tool
      `, [id, userId]);

      // Kiểm tra kết quả
      if (!result || result.length === 0) {
        return null;
      }

      return result[0];
    } catch (error) {
      this.logger.error(`Lỗi khi cập nhật trạng thái active: ${error.message}`, error.stack);
      throw new AppException(CUSTOM_TOOLS_ERROR_CODES.CUSTOM_TOOL_UPDATE_FAILED, 'Cập nhật trạng thái active thất bại');
    }
  }

  /**
   * Kiểm tra xem danh sách tool_id của công cụ tùy chỉnh có tồn tại trong cơ sở dữ liệu không
   * @param toolIds Danh sách ID của các công cụ tùy chỉnh cần kiểm tra
   * @param userId ID của người dùng (tùy chọn, nếu cần kiểm tra quyền sở hữu)
   * @returns Đối tượng chứa thông tin về các ID tồn tại và không tồn tại
   */
  async validateToolIds(
    toolIds: string[],
    userId?: number
  ): Promise<{
    valid: boolean;
    existingIds: string[];
    nonExistingIds: string[];
  }> {
    try {
      // Nếu danh sách rỗng, trả về kết quả ngay lập tức
      if (!toolIds || toolIds.length === 0) {
        return {
          valid: false,
          existingIds: [],
          nonExistingIds: [],
        };
      }

      // Tạo query để tìm các công cụ tùy chỉnh có ID nằm trong danh sách
      const qb = this.createQueryBuilder('customTool')
        .select('customTool.id')
        .where('customTool.id IN (:...toolIds)', { toolIds });

      // Nếu có userId, thêm điều kiện kiểm tra quyền sở hữu
      if (userId) {
        qb.andWhere('customTool.userId = :userId', { userId });
      }

      // Thực hiện truy vấn
      const existingTools = await qb.getMany();

      // Lấy danh sách ID tồn tại
      const existingIds = existingTools.map((tool) => tool.id);

      // Lấy danh sách ID không tồn tại
      const nonExistingIds = toolIds.filter(
        (id) => !existingIds.includes(id),
      );

      return {
        valid: nonExistingIds.length === 0,
        existingIds,
        nonExistingIds,
      };
    } catch (error) {
      this.logger.error(`Lỗi khi kiểm tra ID công cụ tùy chỉnh: ${error.message}`, error.stack);
      return {
        valid: false,
        existingIds: [],
        nonExistingIds: toolIds,
      };
    }
  }
}
