import { Injectable, Logger } from '@nestjs/common';
import { AppException } from '@common/exceptions';
import { UserToolsCustom } from '../../entities';
import { UserToolsCustomRepository } from '../../repositories';
import { CreateUserToolsCustomDto, QueryUserToolsCustomDto, UserToolsCustomResponseDto } from '../dto';
import { CUSTOM_TOOLS_ERROR_CODES } from '../../exceptions';
import { PaginatedResult } from '@common/response/api-response-dto';
import { Transactional } from 'typeorm-transactional';

@Injectable()
export class UserToolsCustomService {
  private readonly logger = new Logger(UserToolsCustomService.name);

  constructor(
    private readonly userToolsCustomRepository: UserToolsCustomRepository,
  ) {}

  /**
   * <PERSON><PERSON><PERSON> danh sách công cụ tùy chỉnh của người dùng
   * @param userId ID của người dùng
   * @param queryDto Tham số truy vấn
   * @returns Danh sách công cụ tùy chỉnh phân trang
   */
  async getUserToolsCustom(
    userId: number,
    queryDto: QueryUserToolsCustomDto,
  ): Promise<PaginatedResult<UserToolsCustomResponseDto>> {
    try {
      const { page, limit, search, method, sortBy, sortDirection } = queryDto;

      // Tạo các tham số truy vấn
      const queryParams: any = {
        userId,
        page,
        limit,
        sortBy,
        sortDirection,
      };

      // Thêm các tham số tìm kiếm nếu có
      if (search) {
        queryParams.search = search;
      }

      // Thêm tham số lọc theo phương thức HTTP nếu có
      if (method) {
        queryParams.method = method;
      }

      // Lấy kết quả từ repository
      const result = await this.userToolsCustomRepository.findByUserId(queryParams);

      // Chuyển đổi các entity thành DTO
      const items = UserToolsCustomResponseDto.fromEntities(result.items);

      // Trả về kết quả với các item đã được chuyển đổi
      return {
        items,
        meta: result.meta
      };
    } catch (error) {
      this.logger.error(`Lỗi khi lấy danh sách công cụ tùy chỉnh: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(CUSTOM_TOOLS_ERROR_CODES.CUSTOM_TOOL_NOT_FOUND, error.message);
    }
  }

  /**
   * Lấy thông tin chi tiết công cụ tùy chỉnh
   * @param id ID của công cụ tùy chỉnh
   * @param userId ID của người dùng
   * @returns Thông tin chi tiết công cụ tùy chỉnh
   */
  async getUserToolCustomById(id: string, userId: number): Promise<UserToolsCustomResponseDto> {
    try {
      const tool = await this.userToolsCustomRepository.findById(id);
      if (!tool || tool.userId !== userId) {
        throw new AppException(CUSTOM_TOOLS_ERROR_CODES.CUSTOM_TOOL_NOT_FOUND);
      }
      return UserToolsCustomResponseDto.fromEntity(tool);
    } catch (error) {
      this.logger.error(`Lỗi khi lấy thông tin công cụ tùy chỉnh: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(CUSTOM_TOOLS_ERROR_CODES.CUSTOM_TOOL_NOT_FOUND, error.message);
    }
  }

  /**
   * Tạo mới công cụ tùy chỉnh
   * @param userId ID của người dùng
   * @param createDto Dữ liệu tạo công cụ tùy chỉnh
   * @returns Công cụ tùy chỉnh đã tạo
   */
  @Transactional()
  async createUserToolCustom(
    userId: number,
    createDto: CreateUserToolsCustomDto,
  ): Promise<UserToolsCustomResponseDto> {
    try {
      // Tạo đối tượng công cụ tùy chỉnh mới
      const newTool = new UserToolsCustom();
      newTool.userId = userId;
      newTool.toolName = createDto.toolName;
      newTool.toolDescription = createDto.toolDescription || null;
      newTool.endpoint = createDto.endpoint;
      newTool.method = createDto.method;
      newTool.response = createDto.response || {};
      newTool.oauthId = createDto.oauthId || null;
      newTool.apiKeyId = createDto.apiKeyId || null;
      newTool.baseUrl = createDto.baseUrl;
      newTool.queryParam = createDto.queryParam || {};
      newTool.pathParam = createDto.pathParam || {};
      newTool.body = createDto.body || {};
      newTool.active = true;

      // Lưu công cụ tùy chỉnh
      const savedTool = await this.userToolsCustomRepository.createCustomTool(newTool);

      // Chuyển đổi entity thành DTO
      return UserToolsCustomResponseDto.fromEntity(savedTool);
    } catch (error) {
      this.logger.error(`Lỗi khi tạo công cụ tùy chỉnh: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(CUSTOM_TOOLS_ERROR_CODES.CUSTOM_TOOL_CREATE_FAILED, error.message);
    }
  }

  /**
   * Cập nhật trạng thái active của công cụ tùy chỉnh
   * @param id ID của công cụ tùy chỉnh
   * @param userId ID của người dùng
   * @returns Thông tin công cụ tùy chỉnh đã cập nhật
   */
  @Transactional()
  async toggleToolCustomActive(id: string, userId: number): Promise<UserToolsCustomResponseDto> {
    try {
      // Sử dụng truy vấn tối ưu để cập nhật và lấy kết quả trong một lần truy vấn
      const updatedTool = await this.userToolsCustomRepository.toggleActiveWithSingleQuery(id, userId);

      // Kiểm tra kết quả
      if (!updatedTool) {
        throw new AppException(CUSTOM_TOOLS_ERROR_CODES.CUSTOM_TOOL_NOT_FOUND);
      }

      // Chuyển đổi kết quả thành DTO
      return UserToolsCustomResponseDto.fromEntity(updatedTool);
    } catch (error) {
      this.logger.error(`Lỗi khi cập nhật trạng thái active: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(CUSTOM_TOOLS_ERROR_CODES.CUSTOM_TOOL_UPDATE_FAILED, error.message);
    }
  }

  /**
   * Xóa công cụ tùy chỉnh
   * @param id ID của công cụ tùy chỉnh
   * @param userId ID của người dùng
   * @returns Void
   */
  @Transactional()
  async deleteToolCustom(id: string, userId: number): Promise<void> {
    try {
      // Kiểm tra công cụ tùy chỉnh có tồn tại không
      const tool = await this.userToolsCustomRepository.findById(id);
      if (!tool || tool.userId !== userId) {
        throw new AppException(CUSTOM_TOOLS_ERROR_CODES.CUSTOM_TOOL_NOT_FOUND);
      }

      // Xóa công cụ tùy chỉnh
      await this.userToolsCustomRepository.deleteCustomTool(id);
    } catch (error) {
      this.logger.error(`Lỗi khi xóa công cụ tùy chỉnh: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(CUSTOM_TOOLS_ERROR_CODES.CUSTOM_TOOL_DELETE_FAILED, error.message);
    }
  }
}
