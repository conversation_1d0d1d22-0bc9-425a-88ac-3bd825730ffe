import { Injectable, Logger } from '@nestjs/common';
import { PDFDocument, PDFFont, PDFPage, rgb, StandardFonts } from 'pdf-lib';
import { PdfPosition, PdfEditResult } from '@shared/interface/pdf-edit.interface';
import { AppException, ErrorCode } from '@common/exceptions/app.exception';
import * as fs from 'fs';
import * as path from 'path';

// Import fontkit với cách khác
const fontkit = require('@pdf-lib/fontkit');

/**
 * Service để chỉnh sửa file PDF
 * Cung cấp các phương thức để thêm text và chữ ký vào PDF
 */
@Injectable()
export class PdfEditService {
  private readonly logger = new Logger(PdfEditService.name);
  private readonly fontPaths = this.getFontPaths();

  constructor() {
    // Kiểm tra xem các font có tồn tại không
    this.checkFontsExist();
  }

  /**
   * Lấy đường dẫn font với fallback cho cả development và production
   * @private
   */
  private getFontPaths() {
    const possiblePaths = [
      // Development path
      {
        regular: path.join(process.cwd(), 'src/shared/lib/fonts/ReemKufiFun-Regular.ttf'),
        bold: path.join(process.cwd(), 'src/shared/lib/fonts/ReemKufiFun-Bold.ttf'),
      },
      // Production path (dist)
      {
        regular: path.join(process.cwd(), 'dist/shared/lib/fonts/ReemKufiFun-Regular.ttf'),
        bold: path.join(process.cwd(), 'dist/shared/lib/fonts/ReemKufiFun-Bold.ttf'),
      },
      // Relative to current file
      {
        regular: path.join(__dirname, '../../lib/fonts/ReemKufiFun-Regular.ttf'),
        bold: path.join(__dirname, '../../lib/fonts/ReemKufiFun-Bold.ttf'),
      },
    ];

    // Tìm path đầu tiên có file tồn tại
    for (const paths of possiblePaths) {
      if (fs.existsSync(paths.regular) && fs.existsSync(paths.bold)) {
        return paths;
      }
    }

    // Fallback về path đầu tiên nếu không tìm thấy
    return possiblePaths[0];
  }

  /**
   * Kiểm tra xem các font cần thiết có tồn tại không
   * @private
   */
  private checkFontsExist(): void {
    try {
      if (!fs.existsSync(this.fontPaths.regular)) {
        this.logger.warn(`Font không tồn tại: ${this.fontPaths.regular}`);
      }
      if (!fs.existsSync(this.fontPaths.bold)) {
        this.logger.warn(`Font không tồn tại: ${this.fontPaths.bold}`);
      }
    } catch (error) {
      this.logger.error('Lỗi khi kiểm tra font', error);
    }
  }

  /**
   * Chỉnh sửa file PDF và trả về file đã chỉnh sửa
   * @param pdfBytes Nội dung PDF gốc (Buffer hoặc Base64 string)
   * @param positions Danh sách các vị trí cần chỉnh sửa
   * @returns Promise<PdfEditResult> Kết quả chỉnh sửa PDF
   */
  async editPdf(pdfBytes: Buffer | string, positions: PdfPosition[]): Promise<PdfEditResult> {
    try {
      // Chuyển đổi input thành Buffer nếu là Base64 string
      let pdfBuffer: Buffer;
      if (Buffer.isBuffer(pdfBytes)) {
        pdfBuffer = pdfBytes;
      } else {
        // Xử lý trường hợp input là data URI
        const base64Data = pdfBytes.startsWith('data:') 
          ? pdfBytes.split(',')[1] 
          : pdfBytes;
        pdfBuffer = Buffer.from(base64Data, 'base64');
      }

      // Tải PDF document
      const pdfDoc = await PDFDocument.load(pdfBuffer);

      // Đăng ký fontkit để hỗ trợ custom fonts NGAY SAU KHI TẠO PDFDocument
      try {
        this.logger.log('Fontkit object:', typeof fontkit);
        pdfDoc.registerFontkit(fontkit);
        this.logger.log('Đã register fontkit cho PDFDocument chính thành công');
      } catch (error) {
        this.logger.error('Lỗi khi register fontkit:', error);
        throw error;
      }
      
      // Tải các font
      const regularFont = await this.loadFont(pdfDoc, 'regular');
      const boldFont = await this.loadFont(pdfDoc, 'bold');

      // Xử lý từng vị trí
      for (const position of positions) {
        const pageIndex = position.pageIndex;
        const pages = pdfDoc.getPages();
        
        if (pageIndex < 0 || pageIndex >= pages.length) {
          this.logger.warn(`Trang ${pageIndex} không tồn tại trong PDF`);
          continue;
        }
        
        const page = pages[pageIndex];
        
        // Chuyển đổi từ mm sang points (1 mm = 2.83465 points)
        const x = position.xMm * 2.83465;
        const y = page.getHeight() - (position.yMm * 2.83465); // Điều chỉnh y cho top-left
        
        // Thêm text vào trang
        if (position.text) {
          const font = position.isCenter ? boldFont : regularFont;
          const fontSize = position.size || 12;
          
          if (position.isCenter) {
            // Tính toán vị trí để căn giữa text
            const textWidth = font.widthOfTextAtSize(position.text, fontSize);
            const xNew = x - textWidth / 2;
            
            page.drawText(position.text, {
              x: xNew,
              y: y,
              size: fontSize,
              font: font,
              color: rgb(0, 0, 0),
            });
          } else {
            page.drawText(position.text, {
              x: x,
              y: y,
              size: fontSize,
              font: font,
              color: rgb(0, 0, 0),
            });
          }
        }
        
        // Thêm chữ ký nếu có
        if (position.signatureBase64) {
          const signatureBytes = Buffer.from(position.signatureBase64, 'base64');
          const signatureImage = await pdfDoc.embedPng(signatureBytes);
          
          // Thiết lập kích thước và vị trí chữ ký
          const signatureWidth = (position.signatureWidthMm || 50) * 2.83465;
          const signatureHeight = (position.signatureHeightMm || 20) * 2.83465;
          
          page.drawImage(signatureImage, {
            x: x,
            y: y - signatureHeight,
            width: signatureWidth,
            height: signatureHeight,
          });
        }
      }
      
      // Lưu PDF đã chỉnh sửa
      const editedPdfBytes = await pdfDoc.save();
      const editedPdfBuffer = Buffer.from(editedPdfBytes);
      
      return {
        pdfBuffer: editedPdfBuffer,
        pdfBase64: editedPdfBuffer.toString('base64'),
      };
    } catch (error) {
      this.logger.error('Lỗi khi chỉnh sửa PDF', error);
      throw new AppException(ErrorCode.PDF_PROCESSING_ERROR, `Lỗi khi chỉnh sửa PDF: ${error.message}`);
    }
  }

  /**
   * Chuyển đổi Buffer thành chuỗi Base64
   * @param buffer Buffer cần chuyển đổi
   * @returns string Chuỗi Base64
   */
  convertBufferToBase64(buffer: Buffer): string {
    if (!buffer) {
      throw new AppException(ErrorCode.VALIDATION_ERROR, 'Dữ liệu không hợp lệ');
    }
    return buffer.toString('base64');
  }

  /**
   * Tải font từ file
   * @param pdfDoc PDFDocument
   * @param fontType Loại font (regular hoặc bold)
   * @returns Promise<PDFFont> Font đã tải
   * @private
   */
  private async loadFont(pdfDoc: PDFDocument, fontType: 'regular' | 'bold'): Promise<PDFFont> {
    try {
      const fontPath = this.fontPaths[fontType];
      this.logger.log(`Đang tải font ${fontType} từ: ${fontPath}`);

      if (!fs.existsSync(fontPath)) {
        this.logger.warn(`Font không tồn tại: ${fontPath}, sử dụng font fallback`);
        return await this.getFallbackFont(pdfDoc, fontType);
      }

      // Fontkit đã được register ở editPdf method

      const fontBytes = fs.readFileSync(fontPath);
      const embeddedFont = await pdfDoc.embedFont(fontBytes);
      this.logger.log(`Đã tải thành công font ${fontType}`);
      return embeddedFont;
    } catch (error) {
      this.logger.error(`Lỗi khi tải font ${fontType}`, error);
      this.logger.warn('Fallback sang font mặc định');
      return await this.getFallbackFont(pdfDoc, fontType);
    }
  }

  /**
   * Lấy font fallback - ưu tiên font ReemKufi nếu có
   * @param pdfDoc PDFDocument
   * @param fontType Loại font
   * @returns Promise<PDFFont>
   * @private
   */
  private async getFallbackFont(pdfDoc: PDFDocument, fontType: 'regular' | 'bold'): Promise<PDFFont> {
    // Fontkit đã được register ở editPdf method

    // Thử tất cả các đường dẫn có thể có cho font ReemKufi
    const allPossiblePaths = [
      // Development paths
      path.join(process.cwd(), 'src/shared/lib/fonts/ReemKufiFun-Regular.ttf'),
      path.join(process.cwd(), 'src/shared/lib/fonts/ReemKufiFun-Bold.ttf'),
      // Production paths
      path.join(process.cwd(), 'dist/shared/lib/fonts/ReemKufiFun-Regular.ttf'),
      path.join(process.cwd(), 'dist/shared/lib/fonts/ReemKufiFun-Bold.ttf'),
      // Relative paths
      path.join(__dirname, '../../lib/fonts/ReemKufiFun-Regular.ttf'),
      path.join(__dirname, '../../lib/fonts/ReemKufiFun-Bold.ttf'),
    ];

    // Thử load font ReemKufi từ bất kỳ path nào có sẵn
    for (const fontPath of allPossiblePaths) {
      try {
        if (fs.existsSync(fontPath)) {
          const fontBytes = fs.readFileSync(fontPath);
          const embeddedFont = await pdfDoc.embedFont(fontBytes);
          this.logger.log(`Đã load thành công font fallback từ: ${fontPath}`);
          return embeddedFont;
        }
      } catch (error) {
        this.logger.warn(`Không thể load font từ ${fontPath}:`, error.message);
        continue;
      }
    }

    // Nếu không load được font ReemKufi, dùng standard font
    try {
      this.logger.warn('Không tìm thấy font ReemKufi, sử dụng Times Roman');
      const standardFont = fontType === 'bold'
        ? StandardFonts.TimesRomanBold
        : StandardFonts.TimesRoman;

      return await pdfDoc.embedFont(standardFont);
    } catch (error) {
      this.logger.error('Lỗi khi tải font Times Roman, fallback về Helvetica', error);
      // Cuối cùng fallback về Helvetica
      return await pdfDoc.embedFont(StandardFonts.Helvetica);
    }
  }
}
