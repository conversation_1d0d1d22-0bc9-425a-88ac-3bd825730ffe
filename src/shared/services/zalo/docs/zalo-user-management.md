# Zalo User Management Service

Service quản lý thông tin người dùng của Zalo Official Account, bao gồm quản lý nhãn, thông tin người dùng và dữ liệu tùy biến.

## Điều kiện sử dụng

Dựa trên tài liệu chính thức của Zalo:

### Quản lý nhãn
- **Gắn nhãn khách hàng**: Cần quyền quản lý người dùng, tối đa 20 nhãn cho mỗi người dùng
- **Gỡ nhãn khách hàng**: Cần quyền quản lý người dùng
- **L<PERSON>y danh sách nhãn**: Cần quyền đọc thông tin người dùng
- **Xóa nhãn**: Cần quyền quản lý người dùng, sẽ tự động gỡ nhãn khỏi tất cả người dùng

### Quản lý người dùng
- **<PERSON><PERSON><PERSON> xu<PERSON>t danh sách người dùng**: <PERSON><PERSON><PERSON> quyền đọc thông tin người dùng, hỗ trợ phân trang
- **Truy xuất chi tiết người dùng**: Cần quyền đọc thông tin người dùng
- **Cập nhật chi tiết người dùng**: Cần quyền quản lý người dùng
- **Xóa thông tin khách hàng**: Cần quyền quản lý người dùng, xóa vĩnh viễn

### Quản lý thông tin tùy biến
- **Truy xuất thông tin tùy biến**: Cần quyền đọc thông tin người dùng
- **Cập nhật thông tin tùy biến**: Cần quyền quản lý người dùng, tối đa 50 trường tùy biến

## API Endpoints

### Quản lý nhãn

#### Gắn nhãn cho người dùng
```typescript
await zaloUserManagementService.assignLabelToUser(
  accessToken,
  'user_123456789',
  'label_123456789'
);
```

#### Gỡ nhãn khỏi người dùng
```typescript
await zaloUserManagementService.removeLabelFromUser(
  accessToken,
  'user_123456789',
  'label_123456789'
);
```

#### Lấy danh sách nhãn
```typescript
const labels = await zaloUserManagementService.getLabels(
  accessToken,
  0,    // offset
  20    // count
);
```

#### Tạo nhãn mới
```typescript
const newLabel = await zaloUserManagementService.createLabel(
  accessToken,
  {
    label_name: 'Khách hàng VIP',
    description: 'Khách hàng có giá trị cao',
    color: '#FF0000'
  }
);
```

#### Xóa nhãn
```typescript
await zaloUserManagementService.deleteLabel(
  accessToken,
  'label_123456789'
);
```

#### Cập nhật nhãn
```typescript
const updatedLabel = await zaloUserManagementService.updateLabel(
  accessToken,
  'label_123456789',
  {
    label_name: 'Khách hàng VVIP',
    description: 'Khách hàng có giá trị rất cao'
  }
);
```

### Quản lý người dùng

#### Lấy danh sách người dùng
```typescript
const users = await zaloUserManagementService.getUsers(
  accessToken,
  0,                    // offset
  20,                   // count
  'label_123456789'     // labelId (optional)
);
```

#### Lấy chi tiết người dùng
```typescript
const userDetail = await zaloUserManagementService.getUserDetail(
  accessToken,
  'user_123456789'
);
```

#### Cập nhật thông tin người dùng
```typescript
await zaloUserManagementService.updateUserDetail(
  accessToken,
  {
    user_id: 'user_123456789',
    display_name: 'Nguyễn Văn B',
    note: 'Khách hàng thân thiết',
    metadata: {
      preference: 'mobile',
      location: 'HCM'
    }
  }
);
```

#### Xóa thông tin người dùng
```typescript
await zaloUserManagementService.deleteUserInfo(
  accessToken,
  'user_123456789'
);
```

#### Tìm kiếm người dùng
```typescript
const searchResults = await zaloUserManagementService.searchUsers(
  accessToken,
  'Nguyễn Văn',  // query
  0,             // offset
  20             // count
);
```

#### Lấy nhãn của người dùng
```typescript
const userLabels = await zaloUserManagementService.getUserLabels(
  accessToken,
  'user_123456789'
);
```

### Quản lý thông tin tùy biến

#### Lấy thông tin tùy biến của người dùng
```typescript
const customInfo = await zaloUserManagementService.getUserCustomInfo(
  accessToken,
  'user_123456789'
);
```

#### Cập nhật thông tin tùy biến
```typescript
await zaloUserManagementService.updateUserCustomInfo(
  accessToken,
  {
    user_id: 'user_123456789',
    custom_fields: {
      company: 'XYZ Corp',
      position: 'Senior Manager',
      interests: ['technology', 'music'],
      score: 95
    }
  }
);
```

## Cấu trúc dữ liệu

### ZaloUserInfo
```typescript
interface ZaloUserInfo {
  id: string;                    // ID người dùng Zalo
  display_name: string;          // Tên hiển thị
  avatar: string;                // URL avatar
  gender?: number;               // Giới tính (1: Nam, 2: Nữ)
  birth_date?: string;           // Ngày sinh (dd/mm/yyyy)
  phone?: string;                // Số điện thoại
}
```

### ZaloUserLabel
```typescript
interface ZaloUserLabel {
  label_id: string;              // ID nhãn
  label_name: string;            // Tên nhãn
  description?: string;          // Mô tả nhãn
  color?: string;                // Màu sắc (hex color)
  created_time?: number;         // Thời gian tạo (Unix timestamp)
  user_count?: number;           // Số lượng người dùng có nhãn
}
```

### ZaloUserCustomInfo
```typescript
interface ZaloUserCustomInfo {
  user_id: string;               // ID người dùng
  custom_fields: Record<string, any>;  // Thông tin tùy biến
  last_updated?: number;         // Thời gian cập nhật cuối
}
```

## Xử lý lỗi

Service tự động xử lý các lỗi từ Zalo API và throw `AppException` với mã lỗi phù hợp:

- `EXTERNAL_SERVICE_ERROR`: Lỗi từ Zalo API
- `VALIDATION_ERROR`: Dữ liệu đầu vào không hợp lệ

## Logging

Service ghi log chi tiết cho tất cả các hoạt động:
- Debug: Thông tin request và tham số
- Error: Chi tiết lỗi và stack trace

## Giới hạn

- Tối đa 20 nhãn cho mỗi người dùng
- Tối đa 50 trường thông tin tùy biến cho mỗi người dùng
- Phân trang: tối đa 50 items mỗi lần gọi API
- Rate limiting theo quy định của Zalo

## Ví dụ sử dụng trong Controller

```typescript
@Controller('zalo/user-management')
export class ZaloUserManagementController {
  constructor(
    private readonly zaloUserManagementService: ZaloUserManagementService,
  ) {}

  @Post('labels/assign')
  async assignLabel(@Body() body: ZaloUserLabelRequestDto) {
    return this.zaloUserManagementService.assignLabelToUser(
      body.access_token,
      body.user_id,
      body.label_id
    );
  }

  @Get('users')
  async getUsers(@Query() params: ZaloUserListParamsDto) {
    return this.zaloUserManagementService.getUsers(
      params.access_token,
      params.offset,
      params.count,
      params.label_id
    );
  }

  @Put('users/custom')
  async updateCustomInfo(@Body() body: ZaloUpdateCustomInfoRequestDto) {
    return this.zaloUserManagementService.updateUserCustomInfo(
      body.access_token,
      body
    );
  }
}
```
