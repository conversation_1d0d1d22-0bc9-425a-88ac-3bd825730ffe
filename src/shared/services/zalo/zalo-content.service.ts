import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { AppException, ErrorCode } from '@common/exceptions';
import { ZaloService } from './zalo.service';
import {
  ZaloContentRequest,
  ZaloContentResponse,
  ZaloContentDetail,
  ZaloContentList,
  ZaloVideoUploadRequest,
  ZaloVideoUploadResponse,
  ZaloContentProcess,
  ZaloUploadFileInfo,
  ZaloVideoContentRequest,
  ZaloVideoContentResponse,
  ZaloVideoContentDetail,
  ZaloVideoContentList,
} from './zalo.interface';

/**
 * Service xử lý các API nội dung của Zalo Official Account
 * 
 * Điều kiện sử dụng từ tài liệu Zalo:
 * - Tạo nội dung: OA cần có quyền tạo nội dung bài viết
 * - Upload video: <PERSON><PERSON><PERSON> thước tối đa 100MB, định dạng MP4, MOV, AVI
 * - Nội dung HTML: Hỗ trợ các thẻ HTML cơ bản, không hỗ trợ JavaScript
 * - Ảnh đại diện: Kích thước tối đa 5MB, định dạng JPG, PNG
 * - Xuất bản: Có thể lên lịch xuất bản hoặc xuất bản ngay
 * - Giới hạn: Mỗi OA có giới hạn số lượng nội dung tạo mỗi ngày
 */
@Injectable()
export class ZaloContentService {
  private readonly logger = new Logger(ZaloContentService.name);
  private readonly baseApiUrl = 'https://openapi.zalo.me/v2.0/oa';

  constructor(
    private readonly zaloService: ZaloService,
    private readonly configService: ConfigService,
  ) {}

  /**
   * Tạo nội dung dạng bài viết
   * @param accessToken Access token của Official Account
   * @param request Thông tin nội dung cần tạo
   * @returns Kết quả tạo nội dung
   */
  async createContent(
    accessToken: string,
    request: ZaloContentRequest,
  ): Promise<ZaloContentResponse> {
    try {
      this.logger.debug(`Creating content: ${request.title}`);
      
      const data = {
        title: request.title,
        description: request.description,
        content: request.content,
        cover_photo_url: request.cover_photo_url,
        video_url: request.video_url,
        status: request.status || 'draft',
        publish_time: request.publish_time,
        tags: request.tags,
        metadata: request.metadata,
      };

      return await this.zaloService.post<ZaloContentResponse>(
        `${this.baseApiUrl}/content/article`,
        accessToken,
        data,
      );
    } catch (error) {
      this.logger.error(`Error creating content: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi tạo nội dung bài viết',
      );
    }
  }

  /**
   * Upload video cho nội dung bài viết
   * @param accessToken Access token của Official Account
   * @param request Thông tin video cần upload
   * @returns Thông tin upload video
   */
  async uploadVideo(
    accessToken: string,
    request: ZaloVideoUploadRequest,
  ): Promise<ZaloVideoUploadResponse> {
    try {
      this.logger.debug(`Uploading video: ${request.filename}`);
      
      // Kiểm tra kích thước file (100MB)
      const maxSize = 100 * 1024 * 1024; // 100MB
      if (request.file_size > maxSize) {
        throw new AppException(
          ErrorCode.VALIDATION_ERROR,
          'Kích thước video vượt quá 100MB',
        );
      }

      // Kiểm tra định dạng video
      const allowedVideoTypes = [
        'video/mp4',
        'video/quicktime', // MOV
        'video/x-msvideo', // AVI
        'video/avi',
      ];

      if (!allowedVideoTypes.includes(request.mime_type)) {
        throw new AppException(
          ErrorCode.VALIDATION_ERROR,
          'Định dạng video không được hỗ trợ. Chỉ hỗ trợ MP4, MOV, AVI',
        );
      }

      const data = {
        filename: request.filename,
        file_size: request.file_size,
        mime_type: request.mime_type,
        description: request.description,
      };

      return await this.zaloService.post<ZaloVideoUploadResponse>(
        `${this.baseApiUrl}/content/video/upload`,
        accessToken,
        data,
      );
    } catch (error) {
      this.logger.error(`Error uploading video: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi upload video',
      );
    }
  }

  /**
   * Upload file video thực tế
   * @param uploadUrl URL upload từ uploadVideo
   * @param uploadToken Token upload
   * @param fileInfo Thông tin file video
   * @returns Kết quả upload
   */
  async uploadVideoFile(
    uploadUrl: string,
    uploadToken: string,
    fileInfo: ZaloUploadFileInfo,
  ): Promise<{ success: boolean; video_url?: string }> {
    try {
      this.logger.debug(`Uploading video file: ${fileInfo.filename}`);
      
      const formData = new FormData();
      const blob = fileInfo.data instanceof Buffer 
        ? new Blob([fileInfo.data], { type: fileInfo.mimetype })
        : new Blob([Buffer.from(typeof fileInfo.data === 'string' ? fileInfo.data : '', 'base64')], { type: fileInfo.mimetype });
      
      formData.append('file', blob, fileInfo.filename);
      formData.append('token', uploadToken);

      return await this.zaloService.postFormData<{ success: boolean; video_url?: string }>(
        uploadUrl,
        '', // Không cần access token cho upload trực tiếp
        formData,
      );
    } catch (error) {
      this.logger.error(`Error uploading video file: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi upload file video',
      );
    }
  }

  /**
   * Kiểm tra tiến trình tạo nội dung
   * @param accessToken Access token của Official Account
   * @param processId ID tiến trình
   * @returns Thông tin tiến trình
   */
  async checkContentProcess(
    accessToken: string,
    processId: string,
  ): Promise<ZaloContentProcess> {
    try {
      this.logger.debug(`Checking content process: ${processId}`);
      
      return await this.zaloService.get<ZaloContentProcess>(
        `${this.baseApiUrl}/content/process/${processId}`,
        accessToken,
      );
    } catch (error) {
      this.logger.error(`Error checking content process: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi kiểm tra tiến trình tạo nội dung',
      );
    }
  }

  /**
   * Lấy chi tiết nội dung
   * @param accessToken Access token của Official Account
   * @param contentId ID của nội dung
   * @returns Chi tiết nội dung
   */
  async getContentDetail(
    accessToken: string,
    contentId: string,
  ): Promise<ZaloContentDetail> {
    try {
      this.logger.debug(`Getting content detail: ${contentId}`);
      
      return await this.zaloService.get<ZaloContentDetail>(
        `${this.baseApiUrl}/content/article/${contentId}`,
        accessToken,
      );
    } catch (error) {
      this.logger.error(`Error getting content detail: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi lấy chi tiết nội dung',
      );
    }
  }

  /**
   * Lấy danh sách nội dung
   * @param accessToken Access token của Official Account
   * @param options Tùy chọn lọc và phân trang
   * @returns Danh sách nội dung
   */
  async getContentList(
    accessToken: string,
    options: {
      status?: 'draft' | 'published' | 'deleted';
      limit?: number;
      offset?: number;
      from_time?: number;
      to_time?: number;
      tags?: string[];
    } = {},
  ): Promise<ZaloContentList> {
    try {
      const params = new URLSearchParams();
      
      if (options.status) params.append('status', options.status);
      if (options.limit) params.append('limit', Math.min(options.limit, 100).toString());
      if (options.offset) params.append('offset', options.offset.toString());
      if (options.from_time) params.append('from_time', options.from_time.toString());
      if (options.to_time) params.append('to_time', options.to_time.toString());
      if (options.tags && options.tags.length > 0) {
        params.append('tags', options.tags.join(','));
      }

      this.logger.debug(`Getting content list with params: ${params.toString()}`);
      
      return await this.zaloService.get<ZaloContentList>(
        `${this.baseApiUrl}/content/article?${params.toString()}`,
        accessToken,
      );
    } catch (error) {
      this.logger.error(`Error getting content list: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi lấy danh sách nội dung',
      );
    }
  }

  /**
   * Cập nhật nội dung
   * @param accessToken Access token của Official Account
   * @param contentId ID của nội dung
   * @param request Thông tin cập nhật
   * @returns Kết quả cập nhật
   */
  async updateContent(
    accessToken: string,
    contentId: string,
    request: Partial<ZaloContentRequest>,
  ): Promise<ZaloContentResponse> {
    try {
      this.logger.debug(`Updating content: ${contentId}`);
      
      const data = {
        title: request.title,
        description: request.description,
        content: request.content,
        cover_photo_url: request.cover_photo_url,
        video_url: request.video_url,
        status: request.status,
        publish_time: request.publish_time,
        tags: request.tags,
        metadata: request.metadata,
      };

      // Loại bỏ các field undefined
      Object.keys(data).forEach(key => {
        if (data[key] === undefined) {
          delete data[key];
        }
      });

      return await this.zaloService.put<ZaloContentResponse>(
        `${this.baseApiUrl}/content/article/${contentId}`,
        accessToken,
        data,
      );
    } catch (error) {
      this.logger.error(`Error updating content: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi cập nhật nội dung',
      );
    }
  }

  /**
   * Xóa nội dung
   * @param accessToken Access token của Official Account
   * @param contentId ID của nội dung
   * @returns Kết quả xóa
   */
  async deleteContent(
    accessToken: string,
    contentId: string,
  ): Promise<{ success: boolean; message?: string }> {
    try {
      this.logger.debug(`Deleting content: ${contentId}`);

      return await this.zaloService.delete<{ success: boolean; message?: string }>(
        `${this.baseApiUrl}/content/article/${contentId}`,
        accessToken,
      );
    } catch (error) {
      this.logger.error(`Error deleting content: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi xóa nội dung',
      );
    }
  }

  /**
   * Xuất bản nội dung
   * @param accessToken Access token của Official Account
   * @param contentId ID của nội dung
   * @param publishTime Thời gian xuất bản (tùy chọn)
   * @returns Kết quả xuất bản
   */
  async publishContent(
    accessToken: string,
    contentId: string,
    publishTime?: number,
  ): Promise<ZaloContentResponse> {
    try {
      this.logger.debug(`Publishing content: ${contentId}`);

      const data = {
        status: 'published' as const,
        publish_time: publishTime || Math.floor(Date.now() / 1000),
      };

      return await this.zaloService.put<ZaloContentResponse>(
        `${this.baseApiUrl}/content/article/${contentId}`,
        accessToken,
        data,
      );
    } catch (error) {
      this.logger.error(`Error publishing content: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi xuất bản nội dung',
      );
    }
  }

  /**
   * Hủy xuất bản nội dung (chuyển về draft)
   * @param accessToken Access token của Official Account
   * @param contentId ID của nội dung
   * @returns Kết quả hủy xuất bản
   */
  async unpublishContent(
    accessToken: string,
    contentId: string,
  ): Promise<ZaloContentResponse> {
    try {
      this.logger.debug(`Unpublishing content: ${contentId}`);

      const data = {
        status: 'draft' as const,
      };

      return await this.zaloService.put<ZaloContentResponse>(
        `${this.baseApiUrl}/content/article/${contentId}`,
        accessToken,
        data,
      );
    } catch (error) {
      this.logger.error(`Error unpublishing content: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi hủy xuất bản nội dung',
      );
    }
  }

  /**
   * Lấy thống kê nội dung
   * @param accessToken Access token của Official Account
   * @param contentId ID của nội dung
   * @returns Thống kê nội dung
   */
  async getContentStatistics(
    accessToken: string,
    contentId: string,
  ): Promise<{
    content_id: string;
    view_count: number;
    like_count: number;
    share_count: number;
    comment_count: number;
    engagement_rate: number;
    daily_stats: Array<{
      date: string;
      views: number;
      likes: number;
      shares: number;
      comments: number;
    }>;
  }> {
    try {
      this.logger.debug(`Getting content statistics: ${contentId}`);

      return await this.zaloService.get<{
        content_id: string;
        view_count: number;
        like_count: number;
        share_count: number;
        comment_count: number;
        engagement_rate: number;
        daily_stats: Array<{
          date: string;
          views: number;
          likes: number;
          shares: number;
          comments: number;
        }>;
      }>(
        `${this.baseApiUrl}/content/article/${contentId}/statistics`,
        accessToken,
      );
    } catch (error) {
      this.logger.error(`Error getting content statistics: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi lấy thống kê nội dung',
      );
    }
  }

  /**
   * Tìm kiếm nội dung
   * @param accessToken Access token của Official Account
   * @param query Từ khóa tìm kiếm
   * @param options Tùy chọn tìm kiếm
   * @returns Kết quả tìm kiếm
   */
  async searchContent(
    accessToken: string,
    query: string,
    options: {
      status?: 'draft' | 'published' | 'deleted';
      limit?: number;
      offset?: number;
      tags?: string[];
    } = {},
  ): Promise<ZaloContentList> {
    try {
      const params = new URLSearchParams();
      params.append('q', query);

      if (options.status) params.append('status', options.status);
      if (options.limit) params.append('limit', Math.min(options.limit, 100).toString());
      if (options.offset) params.append('offset', options.offset.toString());
      if (options.tags && options.tags.length > 0) {
        params.append('tags', options.tags.join(','));
      }

      this.logger.debug(`Searching content with query: ${query}`);

      return await this.zaloService.get<ZaloContentList>(
        `${this.baseApiUrl}/content/article/search?${params.toString()}`,
        accessToken,
      );
    } catch (error) {
      this.logger.error(`Error searching content: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi tìm kiếm nội dung',
      );
    }
  }

  /**
   * Lấy danh sách tags
   * @param accessToken Access token của Official Account
   * @returns Danh sách tags
   */
  async getTags(
    accessToken: string,
  ): Promise<{ tags: Array<{ name: string; count: number }> }> {
    try {
      this.logger.debug('Getting content tags');

      return await this.zaloService.get<{ tags: Array<{ name: string; count: number }> }>(
        `${this.baseApiUrl}/content/tags`,
        accessToken,
      );
    } catch (error) {
      this.logger.error(`Error getting tags: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi lấy danh sách tags',
      );
    }
  }

  /**
   * Kiểm tra trạng thái video
   * @param accessToken Access token của Official Account
   * @param videoId ID của video
   * @returns Trạng thái video
   */
  async getVideoStatus(
    accessToken: string,
    videoId: string,
  ): Promise<ZaloVideoUploadResponse> {
    try {
      this.logger.debug(`Getting video status: ${videoId}`);

      return await this.zaloService.get<ZaloVideoUploadResponse>(
        `${this.baseApiUrl}/content/video/${videoId}`,
        accessToken,
      );
    } catch (error) {
      this.logger.error(`Error getting video status: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi kiểm tra trạng thái video',
      );
    }
  }

  // ==================== VIDEO CONTENT METHODS ====================

  /**
   * Tạo nội dung dạng video
   * @param accessToken Access token của Official Account
   * @param request Thông tin nội dung video cần tạo
   * @returns Kết quả tạo nội dung video
   */
  async createVideoContent(
    accessToken: string,
    request: ZaloVideoContentRequest,
  ): Promise<ZaloVideoContentResponse> {
    try {
      this.logger.debug(`Creating video content: ${request.title}`);

      const data = {
        title: request.title,
        description: request.description,
        video_url: request.video_url,
        thumbnail_url: request.thumbnail_url,
        status: request.status || 'draft',
        publish_time: request.publish_time,
        tags: request.tags,
        metadata: request.metadata,
      };

      return await this.zaloService.post<ZaloVideoContentResponse>(
        `${this.baseApiUrl}/content/video`,
        accessToken,
        data,
      );
    } catch (error) {
      this.logger.error(`Error creating video content: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi tạo nội dung video',
      );
    }
  }

  /**
   * Lấy chi tiết nội dung video
   * @param accessToken Access token của Official Account
   * @param videoContentId ID của nội dung video
   * @returns Chi tiết nội dung video
   */
  async getVideoContentDetail(
    accessToken: string,
    videoContentId: string,
  ): Promise<ZaloVideoContentDetail> {
    try {
      this.logger.debug(`Getting video content detail: ${videoContentId}`);

      return await this.zaloService.get<ZaloVideoContentDetail>(
        `${this.baseApiUrl}/content/video/${videoContentId}`,
        accessToken,
      );
    } catch (error) {
      this.logger.error(`Error getting video content detail: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi lấy chi tiết nội dung video',
      );
    }
  }

  /**
   * Lấy danh sách nội dung video
   * @param accessToken Access token của Official Account
   * @param options Tùy chọn lọc và phân trang
   * @returns Danh sách nội dung video
   */
  async getVideoContentList(
    accessToken: string,
    options: {
      status?: 'draft' | 'published' | 'deleted';
      limit?: number;
      offset?: number;
      from_time?: number;
      to_time?: number;
      tags?: string[];
    } = {},
  ): Promise<ZaloVideoContentList> {
    try {
      const params = new URLSearchParams();

      if (options.status) params.append('status', options.status);
      if (options.limit) params.append('limit', Math.min(options.limit, 100).toString());
      if (options.offset) params.append('offset', options.offset.toString());
      if (options.from_time) params.append('from_time', options.from_time.toString());
      if (options.to_time) params.append('to_time', options.to_time.toString());
      if (options.tags && options.tags.length > 0) {
        params.append('tags', options.tags.join(','));
      }

      this.logger.debug(`Getting video content list with params: ${params.toString()}`);

      return await this.zaloService.get<ZaloVideoContentList>(
        `${this.baseApiUrl}/content/video?${params.toString()}`,
        accessToken,
      );
    } catch (error) {
      this.logger.error(`Error getting video content list: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi lấy danh sách nội dung video',
      );
    }
  }

  /**
   * Cập nhật nội dung video
   * @param accessToken Access token của Official Account
   * @param videoContentId ID của nội dung video
   * @param request Thông tin cập nhật
   * @returns Kết quả cập nhật
   */
  async updateVideoContent(
    accessToken: string,
    videoContentId: string,
    request: Partial<ZaloVideoContentRequest>,
  ): Promise<ZaloVideoContentResponse> {
    try {
      this.logger.debug(`Updating video content: ${videoContentId}`);

      const data = {
        title: request.title,
        description: request.description,
        video_url: request.video_url,
        thumbnail_url: request.thumbnail_url,
        status: request.status,
        publish_time: request.publish_time,
        tags: request.tags,
        metadata: request.metadata,
      };

      // Loại bỏ các field undefined
      Object.keys(data).forEach(key => {
        if (data[key] === undefined) {
          delete data[key];
        }
      });

      return await this.zaloService.put<ZaloVideoContentResponse>(
        `${this.baseApiUrl}/content/video/${videoContentId}`,
        accessToken,
        data,
      );
    } catch (error) {
      this.logger.error(`Error updating video content: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi cập nhật nội dung video',
      );
    }
  }

  /**
   * Xóa nội dung video
   * @param accessToken Access token của Official Account
   * @param videoContentId ID của nội dung video
   * @returns Kết quả xóa
   */
  async deleteVideoContent(
    accessToken: string,
    videoContentId: string,
  ): Promise<{ success: boolean; message?: string }> {
    try {
      this.logger.debug(`Deleting video content: ${videoContentId}`);

      return await this.zaloService.delete<{ success: boolean; message?: string }>(
        `${this.baseApiUrl}/content/video/${videoContentId}`,
        accessToken,
      );
    } catch (error) {
      this.logger.error(`Error deleting video content: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi xóa nội dung video',
      );
    }
  }

  /**
   * Xuất bản nội dung video
   * @param accessToken Access token của Official Account
   * @param videoContentId ID của nội dung video
   * @param publishTime Thời gian xuất bản (tùy chọn)
   * @returns Kết quả xuất bản
   */
  async publishVideoContent(
    accessToken: string,
    videoContentId: string,
    publishTime?: number,
  ): Promise<ZaloVideoContentResponse> {
    try {
      this.logger.debug(`Publishing video content: ${videoContentId}`);

      const data = {
        status: 'published' as const,
        publish_time: publishTime || Math.floor(Date.now() / 1000),
      };

      return await this.zaloService.put<ZaloVideoContentResponse>(
        `${this.baseApiUrl}/content/video/${videoContentId}`,
        accessToken,
        data,
      );
    } catch (error) {
      this.logger.error(`Error publishing video content: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi xuất bản nội dung video',
      );
    }
  }

  /**
   * Hủy xuất bản nội dung video (chuyển về draft)
   * @param accessToken Access token của Official Account
   * @param videoContentId ID của nội dung video
   * @returns Kết quả hủy xuất bản
   */
  async unpublishVideoContent(
    accessToken: string,
    videoContentId: string,
  ): Promise<ZaloVideoContentResponse> {
    try {
      this.logger.debug(`Unpublishing video content: ${videoContentId}`);

      const data = {
        status: 'draft' as const,
      };

      return await this.zaloService.put<ZaloVideoContentResponse>(
        `${this.baseApiUrl}/content/video/${videoContentId}`,
        accessToken,
        data,
      );
    } catch (error) {
      this.logger.error(`Error unpublishing video content: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi hủy xuất bản nội dung video',
      );
    }
  }

  /**
   * Lấy thống kê nội dung video
   * @param accessToken Access token của Official Account
   * @param videoContentId ID của nội dung video
   * @returns Thống kê nội dung video
   */
  async getVideoContentStatistics(
    accessToken: string,
    videoContentId: string,
  ): Promise<{
    video_content_id: string;
    view_count: number;
    like_count: number;
    share_count: number;
    comment_count: number;
    watch_time_total: number;
    watch_time_average: number;
    engagement_rate: number;
    completion_rate: number;
    daily_stats: Array<{
      date: string;
      views: number;
      likes: number;
      shares: number;
      comments: number;
      watch_time: number;
    }>;
  }> {
    try {
      this.logger.debug(`Getting video content statistics: ${videoContentId}`);

      return await this.zaloService.get<{
        video_content_id: string;
        view_count: number;
        like_count: number;
        share_count: number;
        comment_count: number;
        watch_time_total: number;
        watch_time_average: number;
        engagement_rate: number;
        completion_rate: number;
        daily_stats: Array<{
          date: string;
          views: number;
          likes: number;
          shares: number;
          comments: number;
          watch_time: number;
        }>;
      }>(
        `${this.baseApiUrl}/content/video/${videoContentId}/statistics`,
        accessToken,
      );
    } catch (error) {
      this.logger.error(`Error getting video content statistics: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi lấy thống kê nội dung video',
      );
    }
  }

  /**
   * Tìm kiếm nội dung video
   * @param accessToken Access token của Official Account
   * @param query Từ khóa tìm kiếm
   * @param options Tùy chọn tìm kiếm
   * @returns Kết quả tìm kiếm
   */
  async searchVideoContent(
    accessToken: string,
    query: string,
    options: {
      status?: 'draft' | 'published' | 'deleted';
      limit?: number;
      offset?: number;
      tags?: string[];
      duration_min?: number;
      duration_max?: number;
    } = {},
  ): Promise<ZaloVideoContentList> {
    try {
      const params = new URLSearchParams();
      params.append('q', query);

      if (options.status) params.append('status', options.status);
      if (options.limit) params.append('limit', Math.min(options.limit, 100).toString());
      if (options.offset) params.append('offset', options.offset.toString());
      if (options.tags && options.tags.length > 0) {
        params.append('tags', options.tags.join(','));
      }
      if (options.duration_min) params.append('duration_min', options.duration_min.toString());
      if (options.duration_max) params.append('duration_max', options.duration_max.toString());

      this.logger.debug(`Searching video content with query: ${query}`);

      return await this.zaloService.get<ZaloVideoContentList>(
        `${this.baseApiUrl}/content/video/search?${params.toString()}`,
        accessToken,
      );
    } catch (error) {
      this.logger.error(`Error searching video content: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi tìm kiếm nội dung video',
      );
    }
  }
}
