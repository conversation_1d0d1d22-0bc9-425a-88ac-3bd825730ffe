### Test Audience Avatar CDN URL
### Kiểm tra API findOne trả về avatar với CDN URL

@baseUrl = http://localhost:3003/v1
@testToken = eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6MSwidHlwZSI6ImVtcGxveWVlIiwiaWF0IjoxNjI1MDk3NjAwLCJleHAiOjE2MjUxODQwMDB9.test

### 1. Tạo audience test với avatar
POST {{baseUrl}}/admin/marketing/audiences
Authorization: Bearer {{testToken}}
Content-Type: application/json

{
  "name": "Test User Avatar",
  "email": "<EMAIL>",
  "phone": "+84912345678"
}

### 2. Lấy danh sách audience để tìm ID
GET {{baseUrl}}/admin/marketing/audiences?limit=5
Authorization: Bearer {{testToken}}

### 3. Test API findOne với ID audience (thay ID thực tế)
GET {{baseUrl}}/admin/marketing/audiences/1
Authorization: Bearer {{testToken}}

### 4. Cập nhật audience với avatar
PUT {{baseUrl}}/admin/marketing/audiences/1
Authorization: Bearer {{testToken}}
Content-Type: application/json

{
  "name": "Test User Avatar Updated",
  "email": "<EMAIL>",
  "avatarMediaType": "image/jpeg"
}

### 5. Test lại API findOne sau khi có avatar
GET {{baseUrl}}/admin/marketing/audiences/1
Authorization: Bearer {{testToken}}
