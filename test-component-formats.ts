/**
 * Test các format component khác nhau để tìm format đúng
 */

const componentFormats = {
  // Format 1: Object với key là component type
  format1: {
    PARAGRAPH: {
      value: "Test message"
    }
  },

  // Format 2: Object với type property
  format2: {
    type: "PARAGRAPH",
    value: "Test message"
  },

  // Format 3: Object với type property viết thường
  format3: {
    type: "paragraph",
    value: "Test message"
  },

  // Format 4: Object với text property
  format4: {
    type: "PARAGRAPH",
    text: "Test message"
  },

  // Format 5: Object với content property
  format5: {
    type: "PARAGRAPH",
    content: "Test message"
  },

  // Format 6: Chỉ string
  format6: "Test message",

  // Format 7: Array format
  format7: [
    {
      type: "PARAGRAPH",
      value: "Test message"
    }
  ]
};

const templates = Object.entries(componentFormats).map(([formatName, component]) => ({
  name: formatName,
  template: {
    template_name: `Test ${formatName}`,
    template_type: 1,
    tag: "2",
    layout: {
      body: {
        components: Array.isArray(component) ? component : [component]
      }
    },
    tracking_id: `test_${formatName}`
  }
}));

console.log('🧪 Testing different component formats:\n');

templates.forEach(({ name, template }) => {
  console.log(`📋 ${name}:`);
  console.log(JSON.stringify(template, null, 2));
  console.log('');
});

// Template đơn giản nhất để copy-paste
console.log('🎯 Template đơn giản nhất để test (format1):');
console.log(JSON.stringify(templates[0].template, null, 2));
